# 🔍 合同基本信息科室字段深度分析与实现方案

## 📊 问题分析

### 🚨 核心问题
1. **使用科室负责人查询失败** - `use_org_person` 字段无法查出对应的员工姓名
2. **科室明文显示缺失** - 科室字段只显示代码，缺少科室名称的明文显示

### 🔍 数据库表结构分析

#### 核心表关系
```sql
-- 合同主表 (cms_contract)
cms_contract
├── use_org VARCHAR(64)            -- 使用科室代码 (关联 hrm_org.org_id)
├── use_org_person VARCHAR(64)     -- 使用科室负责人 (关联 hrm_employee_info.emp_code)
├── manage_org VARCHAR(64)         -- 管理科室代码 (关联 hrm_org.org_id)
└── responsible_person VARCHAR(64) -- 管理科室负责人 (关联 hrm_employee_info.emp_code)

-- 组织机构表 (hrm_org)
hrm_org
├── org_id VARCHAR(64) PRIMARY KEY -- 组织机构ID
├── org_name VARCHAR(200)          -- 组织机构名称
├── active_flag CHAR(1)            -- 状态 (1:正常 0:停用)
└── hospital_id VARCHAR(50)        -- 医院ID

-- 员工信息表 (hrm_employee_info)
hrm_employee_info
├── emp_code VARCHAR(64) UNIQUE    -- 员工编码
├── emp_name VARCHAR(100)          -- 员工姓名
├── org_id VARCHAR(64)             -- 所属部门ID
├── is_deleted INTEGER             -- 删除标记 (0:正常 1:删除)
└── hospital_id VARCHAR(50)        -- 医院ID
```

## 🛠️ 解决方案

### 1. 后端SQL查询优化

#### 修改 CmsContractReadMapper.xml
```xml
<select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractVo">
    SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
    ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
    bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
    -- 🆕 新增科室和负责人信息关联查询
    use_org_info.org_name AS useOrgName, co.use_org_person AS useOrgPerson,
    use_person_info.emp_name AS useOrgPersonName, co.manage_org AS manageOrg,
    manage_org_info.org_name AS manageOrgName,
    cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
    co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
    hei.emp_name AS responsiblePersonName, co.responsible_phone AS responsiblePhone,
    co.opposite_bank AS oppositeBank, co.opposite_account AS oppositeAccount, co.total_amt AS
    totalAmt, co.payment_type AS paymentType, co.payment_terms AS paymentTerms, co.audit_bchno
    AS auditBchno, co.appyer AS appyer, hei1.emp_name AS appyerName, co.apply_time AS applyTime,
    co.appy_org_id AS appyOrgId, co.chk_state AS chkState, co.crter AS crter, co.create_time AS
    createTime, co.hospital_id AS hospitalId, co.is_deleted AS isDeleted, co.contract_remark AS
    contractRemark, co.opposite_person AS oppositePerson, co.opposite_phone AS oppositePhone,
    co.data_str AS dataStr, co.att_name AS attName, co.att, co.show_key_element AS
    showKeyElement, co.show_pay_terms AS showPayTerms, co.audit_bchno1 AS auditBchno1,
    co.appyer1 AS appyer1, co.chk_state1 AS chkState1, co.validity_start_date AS
    validityStartDate, co.validity_end_date AS validityEndDate, co.ct_copies as ctCopies,
    co.opposite_person_id AS oppositePersonId , co.opposite_bank_id AS oppositeBankId,
    co.ct_copies as ctCopies, co.attachment_names as attachmentNames 
    FROM cms_contract co 
    LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
    LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
    -- 🆕 新增科室和员工信息关联
    LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
    LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
    LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
    LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
    LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
    LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
    <where>
        (co.invalid_flag = '0' or co.invalid_flag is null)
        <!-- 其他查询条件保持不变 -->
    </where>
</select>
```

### 2. Java实体类扩展

#### 修改 CmsContractVo.java
```java
@Data
public class CmsContractVo {
    // 现有字段...
    
    /** 使用科室代码 */
    private String useOrg;
    
    /** 🆕 使用科室名称 */
    private String useOrgName;
    
    /** 使用科室负责人编码 */
    private String useOrgPerson;
    
    /** 🆕 使用科室负责人姓名 */
    private String useOrgPersonName;
    
    /** 管理科室代码 */
    private String manageOrg;
    
    /** 🆕 管理科室名称 */
    private String manageOrgName;
    
    /** 管理科室负责人编码 */
    private String responsiblePerson;
    
    /** 管理科室负责人姓名 */
    private String responsiblePersonName;
}
```

### 3. 前端显示优化

#### 修改 contractPaymentReim.vue
```vue
<template>
  <n-descriptions-item label="使用科室">
    <n-tag v-if="props.contract.useOrgName || props.contract.useOrg" type="info" size="small">
      <template #icon>
        <n-icon><i class="fa fa-building"></i></n-icon>
      </template>
      {{ props.contract.useOrgName || `科室代码: ${props.contract.useOrg}` }}
    </n-tag>
    <span v-else style="color: #999">未设置</span>
  </n-descriptions-item>
  
  <n-descriptions-item label="使用科室负责人">
    <n-tag v-if="props.contract.useOrgPersonName || props.contract.useOrgPerson" type="success" size="small">
      <template #icon>
        <n-icon><i class="fa fa-user"></i></n-icon>
      </template>
      {{ props.contract.useOrgPersonName || `员工编码: ${props.contract.useOrgPerson}` }}
    </n-tag>
    <span v-else style="color: #999">未设置</span>
  </n-descriptions-item>
  
  <n-descriptions-item label="管理科室">
    <n-tag v-if="props.contract.manageOrgName || props.contract.manageOrg" type="warning" size="small">
      <template #icon>
        <n-icon><i class="fa fa-cogs"></i></n-icon>
      </template>
      {{ props.contract.manageOrgName || `科室代码: ${props.contract.manageOrg}` }}
    </n-tag>
    <span v-else style="color: #999">未设置</span>
  </n-descriptions-item>
  
  <n-descriptions-item label="管理科室负责人">
    <n-tag v-if="props.contract.responsiblePersonName || props.contract.responsiblePerson" type="error" size="small">
      <template #icon>
        <n-icon><i class="fa fa-user-tie"></i></n-icon>
      </template>
      {{ props.contract.responsiblePersonName || `员工编码: ${props.contract.responsiblePerson}` }}
    </n-tag>
    <span v-else style="color: #999">未设置</span>
  </n-descriptions-item>
</template>
```

## 🔧 实施步骤

### 步骤1: 数据库验证
```sql
-- 验证关联关系是否正确
SELECT 
    co.id,
    co.ct_name,
    co.use_org,
    use_org_info.org_name AS use_org_name,
    co.use_org_person,
    use_person_info.emp_name AS use_org_person_name,
    co.manage_org,
    manage_org_info.org_name AS manage_org_name,
    co.responsible_person,
    responsible_person_info.emp_name AS responsible_person_name
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info responsible_person_info ON co.responsible_person = responsible_person_info.emp_code AND responsible_person_info.is_deleted = 0
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
LIMIT 10;
```

### 步骤2: 后端代码修改
1. 修改 `CmsContractReadMapper.xml` 中的 `queryList` 方法
2. 在 `CmsContractVo.java` 中添加新的字段
3. 确保 `CmsContractDto.java` 也包含相应的扩展字段

### 步骤3: 前端代码修改
1. 更新 `contractPaymentReim.vue` 中的显示逻辑
2. 添加科室名称的显示支持
3. 优化员工姓名的显示逻辑

### 步骤4: 测试验证
1. 测试科室名称是否正确显示
2. 测试使用科室负责人是否能正确查出
3. 测试管理科室和负责人信息是否完整

## 📋 预期效果

修改完成后，合同基本信息将显示：
- ✅ **使用科室**: 显示科室名称（如：心内科）而非代码
- ✅ **使用科室负责人**: 显示员工姓名（如：张三）而非编码
- ✅ **管理科室**: 显示科室名称（如：设备科）而非代码  
- ✅ **管理科室负责人**: 显示员工姓名（如：李四）而非编码

## 🚨 注意事项

1. **数据完整性**: 确保 `hrm_org` 和 `hrm_employee_info` 表中的数据完整
2. **性能考虑**: 新增的 LEFT JOIN 可能影响查询性能，建议添加相应索引
3. **兼容性**: 保持向后兼容，当名称字段为空时显示代码
4. **数据同步**: 确保组织架构变更时合同中的科室信息能及时更新

## 📈 建议的索引优化

```sql
-- 为提高查询性能，建议添加以下索引
CREATE INDEX idx_hrm_org_id_active ON hrm_org(org_id, active_flag);
CREATE INDEX idx_hrm_employee_code_deleted ON hrm_employee_info(emp_code, is_deleted);
CREATE INDEX idx_cms_contract_org_fields ON cms_contract(use_org, use_org_person, manage_org, responsible_person);
```
