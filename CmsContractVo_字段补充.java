// 🔧 CmsContractVo.java 需要添加的字段
// 在第47行 private String useOrg; 之后添加以下字段：

/** 使用科室 */
private String useOrg;

/** 🆕 使用科室负责人 */
private String useOrgPerson;

/** 🆕 使用科室负责人姓名 */
private String useOrgPersonName;

/** 管理科室 */
private String manageOrg;

// ========================================
// 完整的字段定义（供参考）
// ========================================

package com.jp.med.cms.modules.contractManage.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import java.util.List;

import com.jp.med.cms.modules.common.vo.CmsAuditRcdfmVo;
import lombok.Data;

/**
 * 合同
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
@Data
public class CmsContractVo {

    /** 自增id */
    private Integer id;

    /** 合同编号 */
    private String ctCode;

    /** 合同编号 */
    private String ctUnifiedCode;

    /** 合同名称 */
    private String ctName;

    /** 招标编号 */
    private String tenderNum;

    /** 招标名称 */
    private String tenderName;

    /** 中标时间 */
    private String bidWinningTime;

    /** 签约时间 */
    private String signTime;

    /** 使用科室 */
    private String useOrg;

    /** 🆕 使用科室负责人 */
    private String useOrgPerson;

    /** 🆕 使用科室负责人姓名 */
    private String useOrgPersonName;

    /** 管理科室 */
    private String manageOrg;

    /** 相对方名称 */
    private String oppositeName;

    /** 相对方id */
    private Integer oppositeId;

    /** 院方信息 */
    private Integer mainId;

    /** 合同类型编码 */
    private String ctTypeCode;

    /** 合同状态 */
    private String ctStatus;

    /** 负责人 */
    private String responsiblePerson;

    /** 负责人姓名 */
    private String responsiblePersonName;

    /** 负责人联系电话 */
    private String responsiblePhone;

    // ... 其他现有字段保持不变
}
