# 🔧 使用科室负责人字段修复方案

## 📋 问题确认

根据代码分析，确认了以下问题：

### ✅ 数据库字段存在
- `cms_contract` 表中 **确实有 `use_org_person` 字段**
- `CmsContractDto.java` 中已正确定义该字段

### ❌ 问题所在
1. **SQL查询缺失**: `CmsContractReadMapper.xml` 的 SELECT 语句中没有查询 `co.use_org_person` 字段
2. **VO类字段缺失**: `CmsContractVo.java` 中缺少 `useOrgPerson` 和相关名称字段
3. **关联查询缺失**: 没有关联 `hrm_org` 和 `hrm_employee_info` 表获取名称

## 🛠️ 精确修复步骤

### 步骤1: 修改 CmsContractVo.java

**文件**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/vo/CmsContractVo.java`

在第47行 `private String useOrg;` 之后添加：

```java
/** 使用科室 */
private String useOrg;

/** 🆕 使用科室名称 */
private String useOrgName;

/** 🆕 使用科室负责人 */
private String useOrgPerson;

/** 🆕 使用科室负责人姓名 */
private String useOrgPersonName;

/** 管理科室 */
private String manageOrg;

/** 🆕 管理科室名称 */
private String manageOrgName;
```

### 步骤2: 修改 CmsContractReadMapper.xml

**文件**: `med-cms/src/main/resources/mapper/contractManage/read/CmsContractReadMapper.xml`

#### 2.1 修改 SELECT 语句（第40-44行）

**当前代码**:
```xml
SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, cco.opposite_name AS
oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, co.ct_type_code AS
ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

**修改为**:
```xml
SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
-- 🆕 新增科室和负责人信息
use_org_info.org_name AS useOrgName, co.use_org_person AS useOrgPerson,
use_person_info.emp_name AS useOrgPersonName, co.manage_org AS manageOrg,
manage_org_info.org_name AS manageOrgName,
cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

#### 2.2 修改 FROM 和 JOIN 语句（第62-66行）

**当前代码**:
```xml
FROM cms_contract co LEFT
JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code LEFT JOIN
hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN cms_contract_opposite cco ON
co.opposite_id = cco.id LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id =
coc.id LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

**修改为**:
```xml
FROM cms_contract co 
LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
-- 🆕 新增科室和员工信息关联
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

### 步骤3: 验证数据库关联关系

执行以下SQL验证关联关系是否正确：

```sql
-- 测试关联查询
SELECT 
    co.id,
    co.ct_name,
    co.use_org,
    use_org_info.org_name as use_org_name,
    co.use_org_person,
    use_person_info.emp_name as use_org_person_name,
    co.manage_org,
    manage_org_info.org_name as manage_org_name
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND (co.use_org IS NOT NULL OR co.use_org_person IS NOT NULL)
ORDER BY co.id DESC
LIMIT 5;
```

### 步骤4: 可能的关联字段调整

如果上述查询没有返回预期结果，可能需要调整关联条件：

#### 4.1 如果 use_org 存储的是 org_code 而不是 org_id：
```xml
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_code AND use_org_info.active_flag = '1'
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_code AND manage_org_info.active_flag = '1'
```

#### 4.2 检查字段格式的SQL：
```sql
-- 检查use_org字段的格式
SELECT DISTINCT use_org, LENGTH(use_org) as len 
FROM cms_contract 
WHERE use_org IS NOT NULL 
LIMIT 10;

-- 检查hrm_org表的字段格式
SELECT DISTINCT org_id, org_code, org_name 
FROM hrm_org 
WHERE active_flag = '1' 
LIMIT 10;

-- 检查use_org_person字段的格式
SELECT DISTINCT use_org_person, LENGTH(use_org_person) as len 
FROM cms_contract 
WHERE use_org_person IS NOT NULL 
LIMIT 10;

-- 检查hrm_employee_info表的字段格式
SELECT DISTINCT emp_code, emp_name 
FROM hrm_employee_info 
WHERE is_deleted = 0 
LIMIT 10;
```

### 步骤5: 性能优化索引

```sql
-- 添加性能优化索引
CREATE INDEX IF NOT EXISTS idx_hrm_org_id_active ON hrm_org(org_id, active_flag);
CREATE INDEX IF NOT EXISTS idx_hrm_org_code_active ON hrm_org(org_code, active_flag);
CREATE INDEX IF NOT EXISTS idx_hrm_employee_code_deleted ON hrm_employee_info(emp_code, is_deleted);
CREATE INDEX IF NOT EXISTS idx_cms_contract_org_fields ON cms_contract(use_org, use_org_person, manage_org, responsible_person);
```

## 🧪 测试步骤

1. **修改后端代码**
2. **重启后端服务**
3. **访问合同管理页面**
4. **检查合同基本信息显示**：
   - 使用科室应显示科室名称
   - 使用科室负责人应显示员工姓名
   - 管理科室应显示科室名称

## 🎯 预期结果

修复完成后：
- ✅ **使用科室**: `心内科` (而非 `ORG001`)
- ✅ **使用科室负责人**: `张三` (而非 `EMP001`)
- ✅ **管理科室**: `设备科` (而非 `ORG002`)
- ✅ **管理科室负责人**: `李四` (而非 `EMP002`)

## 🚨 注意事项

1. **备份**: 修改前请备份相关文件
2. **测试环境**: 建议先在测试环境验证
3. **缓存清理**: 重启服务后清理浏览器缓存
4. **日志检查**: 如有问题，检查后端日志中的SQL错误信息

## 📝 关键点总结

- **根本原因**: SQL查询中缺少 `co.use_org_person` 字段和相关关联查询
- **核心修复**: 在SELECT中添加缺失字段，在FROM中添加关联表
- **字段映射**: 确保VO类包含所有需要的字段
- **关联验证**: 通过测试SQL确认关联关系正确
