# 🔧 使用科室负责人字段专项修复

## 📋 问题确认

从接口返回数据中确认：
- ❌ **缺少 `useOrgPerson` 字段** (使用科室负责人代码)
- ❌ **缺少 `useOrgPersonName` 字段** (使用科室负责人姓名)

## 🎯 修复目标

让接口返回使用科室负责人信息：
```json
{
  "useOrgPerson": "0123",          // 🆕 新增
  "useOrgPersonName": "张三",       // 🆕 新增
}
```

## 🛠️ 具体修复步骤

### 步骤1: 修改 CmsContractVo.java

**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/vo/CmsContractVo.java`

在第47行 `private String useOrg;` 之后添加：

```java
/** 使用科室 */
private String useOrg;

/** 🆕 使用科室负责人 */
private String useOrgPerson;

/** 🆕 使用科室负责人姓名 */
private String useOrgPersonName;

/** 管理科室 */
private String manageOrg;
```

### 步骤2: 修改 CmsContractReadMapper.xml

**文件路径**: `med-cms/src/main/resources/mapper/contractManage/read/CmsContractReadMapper.xml`

#### 2.1 在 SELECT 语句中添加字段（约第42行）

**在这行之后**:
```xml
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, cco.opposite_name AS
```

**添加**:
```xml
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
co.use_org_person AS useOrgPerson, use_person_info.emp_name AS useOrgPersonName,
cco.opposite_name AS
```

#### 2.2 在 FROM 语句中添加关联（约第63行）

**在这行之后**:
```xml
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN cms_contract_opposite cco ON
```

**添加**:
```xml
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN cms_contract_opposite cco ON
```

### 步骤3: 验证数据库中是否有数据

执行以下SQL检查：

```sql
-- 检查合同336的use_org_person字段
SELECT id, ct_name, use_org_person 
FROM cms_contract 
WHERE id = 336;

-- 检查use_org_person字段的使用情况
SELECT 
    COUNT(*) as total_contracts,
    COUNT(use_org_person) as has_use_org_person,
    COUNT(CASE WHEN use_org_person IS NOT NULL AND use_org_person != '' THEN 1 END) as has_non_empty_use_org_person
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0;

-- 测试关联查询
SELECT 
    co.id,
    co.ct_name,
    co.use_org_person,
    he.emp_name as use_org_person_name
FROM cms_contract co
LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code AND he.is_deleted = 0
WHERE co.id = 336;
```

## 🧪 测试验证

修改完成后：

1. **重启后端服务**
2. **调用合同查询接口**
3. **检查返回数据是否包含**：
   ```json
   {
     "useOrgPerson": "员工代码",
     "useOrgPersonName": "员工姓名"
   }
   ```

## 🚨 可能的情况

### 情况1: 数据库中use_org_person字段为空
如果查询结果显示 `use_org_person` 为 NULL 或空字符串，说明：
- 该合同确实没有设置使用科室负责人
- 需要在合同录入/编辑时补充该信息

### 情况2: 关联查询无结果
如果 `use_org_person` 有值但关联查询无结果，可能：
- 员工代码在 `hrm_employee_info` 表中不存在
- 员工记录被标记为删除 (`is_deleted = 1`)

## 📋 最小化修改方案

如果只想快速解决显示问题，最小化的修改是：

**只修改 CmsContractReadMapper.xml**:

1. 在 SELECT 中添加：
```xml
co.use_org_person AS useOrgPerson,
use_person_info.emp_name AS useOrgPersonName,
```

2. 在 FROM 中添加：
```xml
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
```

3. 在 CmsContractVo.java 中添加：
```java
private String useOrgPerson;
private String useOrgPersonName;
```

这样修改后，前端就能获取到使用科室负责人的信息了！
