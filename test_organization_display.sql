-- 🔍 合同基本信息科室字段测试查询
-- 用于验证科室和员工信息的关联查询是否正确

-- ==============================================
-- 1. 验证合同表中的组织机构字段数据
-- ==============================================
SELECT 
    '合同表组织机构字段数据检查' as check_type,
    COUNT(*) as total_contracts,
    COUNT(use_org) as has_use_org,
    COUNT(use_org_person) as has_use_org_person,
    COUNT(manage_org) as has_manage_org,
    COUNT(responsible_person) as has_responsible_person
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0;

-- ==============================================
-- 2. 验证hrm_org表中的科室数据
-- ==============================================
SELECT 
    '科室表数据检查' as check_type,
    COUNT(*) as total_orgs,
    COUNT(CASE WHEN active_flag = '1' THEN 1 END) as active_orgs,
    COUNT(CASE WHEN org_name IS NOT NULL AND org_name != '' THEN 1 END) as has_name_orgs
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy';

-- ==============================================
-- 3. 验证hrm_employee_info表中的员工数据
-- ==============================================
SELECT 
    '员工表数据检查' as check_type,
    COUNT(*) as total_employees,
    COUNT(CASE WHEN is_deleted = 0 THEN 1 END) as active_employees,
    COUNT(CASE WHEN emp_name IS NOT NULL AND emp_name != '' THEN 1 END) as has_name_employees
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy';

-- ==============================================
-- 4. 测试完整的关联查询（前10条记录）
-- ==============================================
SELECT 
    co.id,
    co.ct_name,
    co.ct_code,
    -- 使用科室信息
    co.use_org,
    use_org_info.org_name AS use_org_name,
    CASE 
        WHEN use_org_info.org_name IS NOT NULL THEN '✅ 已关联'
        WHEN co.use_org IS NOT NULL THEN '❌ 未关联'
        ELSE '⚪ 未设置'
    END as use_org_status,
    -- 使用科室负责人信息
    co.use_org_person,
    use_person_info.emp_name AS use_org_person_name,
    CASE 
        WHEN use_person_info.emp_name IS NOT NULL THEN '✅ 已关联'
        WHEN co.use_org_person IS NOT NULL THEN '❌ 未关联'
        ELSE '⚪ 未设置'
    END as use_org_person_status,
    -- 管理科室信息
    co.manage_org,
    manage_org_info.org_name AS manage_org_name,
    CASE 
        WHEN manage_org_info.org_name IS NOT NULL THEN '✅ 已关联'
        WHEN co.manage_org IS NOT NULL THEN '❌ 未关联'
        ELSE '⚪ 未设置'
    END as manage_org_status,
    -- 管理科室负责人信息
    co.responsible_person,
    responsible_person_info.emp_name AS responsible_person_name,
    CASE 
        WHEN responsible_person_info.emp_name IS NOT NULL THEN '✅ 已关联'
        WHEN co.responsible_person IS NOT NULL THEN '❌ 未关联'
        ELSE '⚪ 未设置'
    END as responsible_person_status
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info responsible_person_info ON co.responsible_person = responsible_person_info.emp_code AND responsible_person_info.is_deleted = 0
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
ORDER BY co.id DESC
LIMIT 10;

-- ==============================================
-- 5. 统计关联成功率
-- ==============================================
WITH contract_org_stats AS (
    SELECT 
        co.id,
        CASE WHEN use_org_info.org_name IS NOT NULL THEN 1 ELSE 0 END as use_org_linked,
        CASE WHEN use_person_info.emp_name IS NOT NULL THEN 1 ELSE 0 END as use_person_linked,
        CASE WHEN manage_org_info.org_name IS NOT NULL THEN 1 ELSE 0 END as manage_org_linked,
        CASE WHEN responsible_person_info.emp_name IS NOT NULL THEN 1 ELSE 0 END as responsible_person_linked,
        CASE WHEN co.use_org IS NOT NULL THEN 1 ELSE 0 END as has_use_org,
        CASE WHEN co.use_org_person IS NOT NULL THEN 1 ELSE 0 END as has_use_person,
        CASE WHEN co.manage_org IS NOT NULL THEN 1 ELSE 0 END as has_manage_org,
        CASE WHEN co.responsible_person IS NOT NULL THEN 1 ELSE 0 END as has_responsible_person
    FROM cms_contract co
    LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
    LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
    LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
    LEFT JOIN hrm_employee_info responsible_person_info ON co.responsible_person = responsible_person_info.emp_code AND responsible_person_info.is_deleted = 0
    WHERE co.hospital_id = 'zjxrmyy' 
      AND co.is_deleted = 0
)
SELECT 
    '关联成功率统计' as stats_type,
    COUNT(*) as total_contracts,
    -- 使用科室统计
    SUM(has_use_org) as contracts_with_use_org,
    SUM(use_org_linked) as use_org_linked_count,
    ROUND(
        CASE WHEN SUM(has_use_org) > 0 
        THEN (SUM(use_org_linked)::decimal / SUM(has_use_org)) * 100 
        ELSE 0 END, 2
    ) as use_org_link_rate,
    -- 使用科室负责人统计
    SUM(has_use_person) as contracts_with_use_person,
    SUM(use_person_linked) as use_person_linked_count,
    ROUND(
        CASE WHEN SUM(has_use_person) > 0 
        THEN (SUM(use_person_linked)::decimal / SUM(has_use_person)) * 100 
        ELSE 0 END, 2
    ) as use_person_link_rate,
    -- 管理科室统计
    SUM(has_manage_org) as contracts_with_manage_org,
    SUM(manage_org_linked) as manage_org_linked_count,
    ROUND(
        CASE WHEN SUM(has_manage_org) > 0 
        THEN (SUM(manage_org_linked)::decimal / SUM(has_manage_org)) * 100 
        ELSE 0 END, 2
    ) as manage_org_link_rate,
    -- 管理科室负责人统计
    SUM(has_responsible_person) as contracts_with_responsible_person,
    SUM(responsible_person_linked) as responsible_person_linked_count,
    ROUND(
        CASE WHEN SUM(has_responsible_person) > 0 
        THEN (SUM(responsible_person_linked)::decimal / SUM(has_responsible_person)) * 100 
        ELSE 0 END, 2
    ) as responsible_person_link_rate
FROM contract_org_stats;

-- ==============================================
-- 6. 查找关联失败的记录（用于数据修复）
-- ==============================================
SELECT 
    '关联失败记录' as issue_type,
    co.id,
    co.ct_name,
    co.ct_code,
    CASE 
        WHEN co.use_org IS NOT NULL AND use_org_info.org_name IS NULL THEN '使用科室关联失败: ' || co.use_org
        WHEN co.use_org_person IS NOT NULL AND use_person_info.emp_name IS NULL THEN '使用科室负责人关联失败: ' || co.use_org_person
        WHEN co.manage_org IS NOT NULL AND manage_org_info.org_name IS NULL THEN '管理科室关联失败: ' || co.manage_org
        WHEN co.responsible_person IS NOT NULL AND responsible_person_info.emp_name IS NULL THEN '管理科室负责人关联失败: ' || co.responsible_person
        ELSE '无关联问题'
    END as issue_detail
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info responsible_person_info ON co.responsible_person = responsible_person_info.emp_code AND responsible_person_info.is_deleted = 0
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND (
    (co.use_org IS NOT NULL AND use_org_info.org_name IS NULL) OR
    (co.use_org_person IS NOT NULL AND use_person_info.emp_name IS NULL) OR
    (co.manage_org IS NOT NULL AND manage_org_info.org_name IS NULL) OR
    (co.responsible_person IS NOT NULL AND responsible_person_info.emp_name IS NULL)
  )
ORDER BY co.id DESC
LIMIT 20;

-- ==============================================
-- 7. 建议的索引创建语句（提高查询性能）
-- ==============================================
/*
-- 执行以下SQL创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_hrm_org_id_active ON hrm_org(org_id, active_flag);
CREATE INDEX IF NOT EXISTS idx_hrm_employee_code_deleted ON hrm_employee_info(emp_code, is_deleted);
CREATE INDEX IF NOT EXISTS idx_cms_contract_org_fields ON cms_contract(use_org, use_org_person, manage_org, responsible_person);
CREATE INDEX IF NOT EXISTS idx_cms_contract_hospital_deleted ON cms_contract(hospital_id, is_deleted);
*/
