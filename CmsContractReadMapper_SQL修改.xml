<!-- 🔧 CmsContractReadMapper.xml 需要修改的SQL -->

<!-- 1. 在 SELECT 语句中添加字段（约第42行） -->
<!-- 原来的代码： -->
<!--
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, cco.opposite_name AS
oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, co.ct_type_code AS
ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
-->

<!-- 修改为： -->
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
co.use_org_person AS useOrgPerson, use_person_info.emp_name AS useOrgPersonName,
cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,

<!-- 2. 在 FROM 语句中添加关联（约第63行） -->
<!-- 原来的代码： -->
<!--
FROM cms_contract co LEFT
JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code LEFT JOIN
hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN cms_contract_opposite cco ON
co.opposite_id = cco.id LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id =
coc.id LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
-->

<!-- 修改为： -->
FROM cms_contract co 
LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id

<!-- ========================================== -->
<!-- 完整的修改示例 -->
<!-- ========================================== -->

<select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractVo">
    SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
    ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
    bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
    -- 🆕 新增使用科室负责人字段
    co.use_org_person AS useOrgPerson, use_person_info.emp_name AS useOrgPersonName,
    cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
    co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
    hei.emp_name AS responsiblePersonName, co.responsible_phone AS responsiblePhone,
    co.opposite_bank AS oppositeBank, co.opposite_account AS oppositeAccount, co.total_amt AS
    totalAmt, co.payment_type AS paymentType, co.payment_terms AS paymentTerms, co.audit_bchno
    AS auditBchno, co.appyer AS appyer, hei1.emp_name AS appyerName, co.apply_time AS applyTime,
    co.appy_org_id AS appyOrgId, co.chk_state AS chkState, co.crter AS crter, co.create_time AS
    createTime, co.hospital_id AS hospitalId, co.is_deleted AS isDeleted, co.contract_remark AS
    contractRemark, co.opposite_person AS oppositePerson, co.opposite_phone AS oppositePhone,
    co.data_str AS dataStr, co.att_name AS attName, co.att, co.show_key_element AS
    showKeyElement, co.show_pay_terms AS showPayTerms, co.audit_bchno1 AS auditBchno1,
    co.appyer1 AS appyer1, co.apply_time1 AS applyTime1, co.chk_state1 AS chkState1,
    co.validity_start_date AS validityStartDate, co.validity_end_date AS validityEndDate,
    co.renewal AS renewal, co.last_contract_id AS lastContractId, co.invalid_flag AS
    invalidFlag, co.draft_type AS draftType, co.draft_process_instance_code AS
    draftProcessInstanceCode, co.signed_process_instance_code AS signedProcessInstanceCode,
    coc.contact_name AS contactName, coc.contact_phone AS contactPhone, cof.opening_bank AS
    openingBank, cof.bank_account AS bankAccount, co.total_amt_in_word AS totalAmtInWord,
    co.opposite_person_id AS oppositePersonId , co.opposite_bank_id AS oppositeBankId,
    co.ct_copies as ctCopies, co.attachment_names as attachmentNames 
    FROM cms_contract co 
    LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
    LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
    -- 🆕 新增使用科室负责人关联
    LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
    LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
    LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
    LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id 
    <where>
        (co.invalid_flag = '0' or co.invalid_flag is null) 
        <if test="chkState != null and chkState != '' "> 
            and co.chk_state = #{chkState,jdbcType=VARCHAR} 
        </if>
        <!-- 其他WHERE条件保持不变 -->
    </where>
</select>

<!-- ========================================== -->
<!-- 验证SQL（在修改前执行） -->
<!-- ========================================== -->

-- 检查use_org_person字段是否有数据
SELECT id, ct_name, use_org_person 
FROM cms_contract 
WHERE id = 336;

-- 测试关联查询
SELECT 
    co.id,
    co.ct_name,
    co.use_org_person,
    he.emp_name as use_org_person_name
FROM cms_contract co
LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code AND he.is_deleted = 0
WHERE co.id = 336;
