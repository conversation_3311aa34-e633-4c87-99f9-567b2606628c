-- 🔍 合同科室字段数据库验证查询
-- 请在数据库中执行以下查询来验证数据结构和内容

-- ==============================================
-- 1. 检查cms_contract表的字段结构
-- ==============================================
SELECT 
    '合同表字段检查' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'cms_contract' 
  AND column_name IN ('use_org', 'use_org_person', 'manage_org', 'responsible_person')
ORDER BY ordinal_position;

-- ==============================================
-- 2. 检查合同表中的实际数据（前5条有组织机构信息的记录）
-- ==============================================
SELECT 
    '合同表数据样例' as data_type,
    id,
    ct_name,
    ct_code,
    use_org,
    use_org_person,
    manage_org,
    responsible_person,
    hospital_id,
    create_time
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
  AND (use_org IS NOT NULL OR use_org_person IS NOT NULL OR manage_org IS NOT NULL OR responsible_person IS NOT NULL)
ORDER BY id DESC
LIMIT 5;

-- ==============================================
-- 3. 检查hrm_org表的字段结构
-- ==============================================
SELECT 
    'hrm_org表字段检查' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'hrm_org' 
  AND column_name IN ('org_id', 'org_name', 'org_code', 'active_flag', 'hospital_id')
ORDER BY ordinal_position;

-- ==============================================
-- 4. 检查hrm_org表中的实际数据（前5条）
-- ==============================================
SELECT 
    'hrm_org表数据样例' as data_type,
    org_id,
    org_name,
    org_code,
    active_flag,
    hospital_id
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' 
  AND active_flag = '1'
ORDER BY org_id
LIMIT 5;

-- ==============================================
-- 5. 检查hrm_employee_info表的字段结构
-- ==============================================
SELECT 
    'hrm_employee_info表字段检查' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'hrm_employee_info' 
  AND column_name IN ('emp_code', 'emp_name', 'org_id', 'is_deleted', 'hospital_id')
ORDER BY ordinal_position;

-- ==============================================
-- 6. 检查hrm_employee_info表中的实际数据（前5条）
-- ==============================================
SELECT 
    'hrm_employee_info表数据样例' as data_type,
    emp_code,
    emp_name,
    org_id,
    is_deleted,
    hospital_id
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
ORDER BY emp_code
LIMIT 5;

-- ==============================================
-- 7. 测试关联查询 - 使用org_id关联
-- ==============================================
SELECT 
    '使用org_id关联测试' as test_type,
    co.id,
    co.ct_name,
    co.use_org,
    use_org_info.org_name as use_org_name_by_id,
    co.use_org_person,
    use_person_info.emp_name as use_org_person_name,
    co.manage_org,
    manage_org_info.org_name as manage_org_name_by_id,
    co.responsible_person,
    responsible_person_info.emp_name as responsible_person_name
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info responsible_person_info ON co.responsible_person = responsible_person_info.emp_code AND responsible_person_info.is_deleted = 0
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND (co.use_org IS NOT NULL OR co.use_org_person IS NOT NULL)
ORDER BY co.id DESC
LIMIT 5;

-- ==============================================
-- 8. 测试关联查询 - 使用org_code关联
-- ==============================================
SELECT 
    '使用org_code关联测试' as test_type,
    co.id,
    co.ct_name,
    co.use_org,
    use_org_info.org_name as use_org_name_by_code,
    co.manage_org,
    manage_org_info.org_name as manage_org_name_by_code
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_code AND use_org_info.active_flag = '1'
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_code AND manage_org_info.active_flag = '1'
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND (co.use_org IS NOT NULL OR co.manage_org IS NOT NULL)
ORDER BY co.id DESC
LIMIT 5;

-- ==============================================
-- 9. 检查use_org字段的数据格式
-- ==============================================
SELECT 
    '使用科室字段格式分析' as analysis_type,
    use_org,
    COUNT(*) as count,
    MIN(LENGTH(use_org)) as min_length,
    MAX(LENGTH(use_org)) as max_length,
    GROUP_CONCAT(DISTINCT SUBSTRING(use_org, 1, 10) SEPARATOR ', ') as sample_values
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
  AND use_org IS NOT NULL
GROUP BY use_org
ORDER BY count DESC
LIMIT 10;

-- ==============================================
-- 10. 检查use_org_person字段的数据格式
-- ==============================================
SELECT 
    '使用科室负责人字段格式分析' as analysis_type,
    use_org_person,
    COUNT(*) as count,
    MIN(LENGTH(use_org_person)) as min_length,
    MAX(LENGTH(use_org_person)) as max_length
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
  AND use_org_person IS NOT NULL
GROUP BY use_org_person
ORDER BY count DESC
LIMIT 10;

-- ==============================================
-- 11. 检查hrm_org表中org_id和org_code的格式
-- ==============================================
SELECT 
    'hrm_org字段格式分析' as analysis_type,
    'org_id' as field_name,
    MIN(LENGTH(org_id)) as min_length,
    MAX(LENGTH(org_id)) as max_length,
    COUNT(DISTINCT org_id) as distinct_count,
    GROUP_CONCAT(DISTINCT SUBSTRING(org_id, 1, 10) SEPARATOR ', ') as sample_values
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' 
  AND active_flag = '1'

UNION ALL

SELECT 
    'hrm_org字段格式分析' as analysis_type,
    'org_code' as field_name,
    MIN(LENGTH(org_code)) as min_length,
    MAX(LENGTH(org_code)) as max_length,
    COUNT(DISTINCT org_code) as distinct_count,
    GROUP_CONCAT(DISTINCT SUBSTRING(org_code, 1, 10) SEPARATOR ', ') as sample_values
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' 
  AND active_flag = '1'
  AND org_code IS NOT NULL;

-- ==============================================
-- 12. 检查hrm_employee_info表中emp_code的格式
-- ==============================================
SELECT 
    'hrm_employee_info字段格式分析' as analysis_type,
    MIN(LENGTH(emp_code)) as min_length,
    MAX(LENGTH(emp_code)) as max_length,
    COUNT(DISTINCT emp_code) as distinct_count,
    GROUP_CONCAT(DISTINCT SUBSTRING(emp_code, 1, 10) SEPARATOR ', ') as sample_values
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0;

-- ==============================================
-- 13. 最终关联成功率统计
-- ==============================================
WITH link_stats AS (
    SELECT 
        co.id,
        co.use_org,
        co.use_org_person,
        co.manage_org,
        co.responsible_person,
        -- 尝试用org_id关联
        use_org_by_id.org_name as use_org_name_by_id,
        manage_org_by_id.org_name as manage_org_name_by_id,
        -- 尝试用org_code关联
        use_org_by_code.org_name as use_org_name_by_code,
        manage_org_by_code.org_name as manage_org_name_by_code,
        -- 员工关联
        use_person.emp_name as use_person_name,
        responsible_person.emp_name as responsible_person_name
    FROM cms_contract co
    LEFT JOIN hrm_org use_org_by_id ON co.use_org = use_org_by_id.org_id AND use_org_by_id.active_flag = '1'
    LEFT JOIN hrm_org manage_org_by_id ON co.manage_org = manage_org_by_id.org_id AND manage_org_by_id.active_flag = '1'
    LEFT JOIN hrm_org use_org_by_code ON co.use_org = use_org_by_code.org_code AND use_org_by_code.active_flag = '1'
    LEFT JOIN hrm_org manage_org_by_code ON co.manage_org = manage_org_by_code.org_code AND manage_org_by_code.active_flag = '1'
    LEFT JOIN hrm_employee_info use_person ON co.use_org_person = use_person.emp_code AND use_person.is_deleted = 0
    LEFT JOIN hrm_employee_info responsible_person ON co.responsible_person = responsible_person.emp_code AND responsible_person.is_deleted = 0
    WHERE co.hospital_id = 'zjxrmyy' 
      AND co.is_deleted = 0
      AND (co.use_org IS NOT NULL OR co.use_org_person IS NOT NULL OR co.manage_org IS NOT NULL OR co.responsible_person IS NOT NULL)
)
SELECT 
    '关联成功率统计' as stats_type,
    COUNT(*) as total_records,
    -- 使用科室统计
    COUNT(CASE WHEN use_org IS NOT NULL THEN 1 END) as has_use_org,
    COUNT(CASE WHEN use_org_name_by_id IS NOT NULL THEN 1 END) as use_org_linked_by_id,
    COUNT(CASE WHEN use_org_name_by_code IS NOT NULL THEN 1 END) as use_org_linked_by_code,
    -- 使用科室负责人统计
    COUNT(CASE WHEN use_org_person IS NOT NULL THEN 1 END) as has_use_org_person,
    COUNT(CASE WHEN use_person_name IS NOT NULL THEN 1 END) as use_person_linked,
    -- 管理科室统计
    COUNT(CASE WHEN manage_org IS NOT NULL THEN 1 END) as has_manage_org,
    COUNT(CASE WHEN manage_org_name_by_id IS NOT NULL THEN 1 END) as manage_org_linked_by_id,
    COUNT(CASE WHEN manage_org_name_by_code IS NOT NULL THEN 1 END) as manage_org_linked_by_code,
    -- 管理科室负责人统计
    COUNT(CASE WHEN responsible_person IS NOT NULL THEN 1 END) as has_responsible_person,
    COUNT(CASE WHEN responsible_person_name IS NOT NULL THEN 1 END) as responsible_person_linked
FROM link_stats;
