-- 🔍 检查合同表和组织机构表的实际数据结构和内容

-- ==============================================
-- 1. 检查cms_contract表的组织机构相关字段结构
-- ==============================================
SELECT 
    '合同表字段结构检查' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'cms_contract' 
  AND column_name IN ('use_org', 'use_org_person', 'manage_org', 'responsible_person')
ORDER BY ordinal_position;

-- ==============================================
-- 2. 检查cms_contract表中的实际数据（前10条）
-- ==============================================
SELECT 
    '合同表数据样例' as data_type,
    id,
    ct_name,
    ct_code,
    use_org,
    use_org_person,
    manage_org,
    responsible_person,
    hospital_id
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
  AND (use_org IS NOT NULL OR use_org_person IS NOT NULL OR manage_org IS NOT NULL OR responsible_person IS NOT NULL)
ORDER BY id DESC
LIMIT 10;

-- ==============================================
-- 3. 检查hrm_org表结构
-- ==============================================
SELECT 
    'hrm_org表字段结构' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'hrm_org' 
  AND column_name IN ('org_id', 'org_name', 'org_code', 'active_flag', 'hospital_id')
ORDER BY ordinal_position;

-- ==============================================
-- 4. 检查hrm_org表中的实际数据（前10条）
-- ==============================================
SELECT 
    'hrm_org表数据样例' as data_type,
    org_id,
    org_name,
    org_code,
    active_flag,
    hospital_id
FROM hrm_org 
WHERE hospital_id = 'zjxrmyy' 
  AND active_flag = '1'
ORDER BY org_id
LIMIT 10;

-- ==============================================
-- 5. 检查hrm_employee_info表结构
-- ==============================================
SELECT 
    'hrm_employee_info表字段结构' as check_type,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'hrm_employee_info' 
  AND column_name IN ('emp_code', 'emp_name', 'org_id', 'is_deleted', 'hospital_id')
ORDER BY ordinal_position;

-- ==============================================
-- 6. 检查hrm_employee_info表中的实际数据（前10条）
-- ==============================================
SELECT 
    'hrm_employee_info表数据样例' as data_type,
    emp_code,
    emp_name,
    org_id,
    is_deleted,
    hospital_id
FROM hrm_employee_info 
WHERE hospital_id = 'zjxrmyy' 
  AND is_deleted = 0
ORDER BY emp_code
LIMIT 10;

-- ==============================================
-- 7. 检查合同表中use_org字段与hrm_org表的关联情况
-- ==============================================
SELECT 
    '使用科室关联检查' as check_type,
    co.use_org as contract_use_org,
    ho.org_id as hrm_org_id,
    ho.org_name as hrm_org_name,
    ho.org_code as hrm_org_code,
    CASE 
        WHEN ho.org_name IS NOT NULL THEN '✅ 关联成功'
        ELSE '❌ 关联失败'
    END as link_status
FROM cms_contract co
LEFT JOIN hrm_org ho ON co.use_org = ho.org_id AND ho.active_flag = '1' AND ho.hospital_id = 'zjxrmyy'
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND co.use_org IS NOT NULL
ORDER BY co.id DESC
LIMIT 10;

-- ==============================================
-- 8. 检查合同表中use_org字段与hrm_org表的org_code关联情况
-- ==============================================
SELECT 
    '使用科室org_code关联检查' as check_type,
    co.use_org as contract_use_org,
    ho.org_id as hrm_org_id,
    ho.org_name as hrm_org_name,
    ho.org_code as hrm_org_code,
    CASE 
        WHEN ho.org_name IS NOT NULL THEN '✅ 关联成功'
        ELSE '❌ 关联失败'
    END as link_status
FROM cms_contract co
LEFT JOIN hrm_org ho ON co.use_org = ho.org_code AND ho.active_flag = '1' AND ho.hospital_id = 'zjxrmyy'
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND co.use_org IS NOT NULL
ORDER BY co.id DESC
LIMIT 10;

-- ==============================================
-- 9. 检查合同表中use_org_person字段与hrm_employee_info表的关联情况
-- ==============================================
SELECT 
    '使用科室负责人关联检查' as check_type,
    co.use_org_person as contract_use_org_person,
    he.emp_code as hrm_emp_code,
    he.emp_name as hrm_emp_name,
    he.org_id as hrm_emp_org_id,
    CASE 
        WHEN he.emp_name IS NOT NULL THEN '✅ 关联成功'
        ELSE '❌ 关联失败'
    END as link_status
FROM cms_contract co
LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code AND he.is_deleted = 0 AND he.hospital_id = 'zjxrmyy'
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND co.use_org_person IS NOT NULL
ORDER BY co.id DESC
LIMIT 10;

-- ==============================================
-- 10. 统计各种关联方式的成功率
-- ==============================================
WITH link_stats AS (
    SELECT 
        co.id,
        co.use_org,
        co.use_org_person,
        -- 尝试用org_id关联
        ho1.org_name as org_name_by_id,
        -- 尝试用org_code关联
        ho2.org_name as org_name_by_code,
        -- 员工关联
        he.emp_name as emp_name_linked
    FROM cms_contract co
    LEFT JOIN hrm_org ho1 ON co.use_org = ho1.org_id AND ho1.active_flag = '1' AND ho1.hospital_id = 'zjxrmyy'
    LEFT JOIN hrm_org ho2 ON co.use_org = ho2.org_code AND ho2.active_flag = '1' AND ho2.hospital_id = 'zjxrmyy'
    LEFT JOIN hrm_employee_info he ON co.use_org_person = he.emp_code AND he.is_deleted = 0 AND he.hospital_id = 'zjxrmyy'
    WHERE co.hospital_id = 'zjxrmyy' 
      AND co.is_deleted = 0
      AND (co.use_org IS NOT NULL OR co.use_org_person IS NOT NULL)
)
SELECT 
    '关联方式统计' as stats_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN use_org IS NOT NULL THEN 1 END) as has_use_org,
    COUNT(CASE WHEN org_name_by_id IS NOT NULL THEN 1 END) as org_linked_by_id,
    COUNT(CASE WHEN org_name_by_code IS NOT NULL THEN 1 END) as org_linked_by_code,
    COUNT(CASE WHEN use_org_person IS NOT NULL THEN 1 END) as has_use_org_person,
    COUNT(CASE WHEN emp_name_linked IS NOT NULL THEN 1 END) as emp_linked,
    ROUND(
        CASE WHEN COUNT(CASE WHEN use_org IS NOT NULL THEN 1 END) > 0 
        THEN (COUNT(CASE WHEN org_name_by_id IS NOT NULL THEN 1 END)::decimal / COUNT(CASE WHEN use_org IS NOT NULL THEN 1 END)) * 100 
        ELSE 0 END, 2
    ) as org_id_link_rate,
    ROUND(
        CASE WHEN COUNT(CASE WHEN use_org IS NOT NULL THEN 1 END) > 0 
        THEN (COUNT(CASE WHEN org_name_by_code IS NOT NULL THEN 1 END)::decimal / COUNT(CASE WHEN use_org IS NOT NULL THEN 1 END)) * 100 
        ELSE 0 END, 2
    ) as org_code_link_rate,
    ROUND(
        CASE WHEN COUNT(CASE WHEN use_org_person IS NOT NULL THEN 1 END) > 0 
        THEN (COUNT(CASE WHEN emp_name_linked IS NOT NULL THEN 1 END)::decimal / COUNT(CASE WHEN use_org_person IS NOT NULL THEN 1 END)) * 100 
        ELSE 0 END, 2
    ) as emp_link_rate
FROM link_stats;
