<template>
  <div class="organization-info-test">
    <h3>🧪 组织机构信息显示测试</h3>
    
    <n-descriptions bordered :column="2" style="margin-bottom: 24px" label-placement="left">
      <!-- 使用科室 -->
      <n-descriptions-item label="使用科室">
        <n-tag v-if="contract.useOrgName || contract.useOrg" type="info" size="small">
          <template #icon>
            <n-icon><i class="fa fa-building"></i></n-icon>
          </template>
          {{ contract.useOrgName || `科室代码: ${contract.useOrg}` }}
        </n-tag>
        <span v-else style="color: #999">未设置</span>
      </n-descriptions-item>
      
      <!-- 使用科室负责人 -->
      <n-descriptions-item label="使用科室负责人">
        <n-tag v-if="contract.useOrgPersonName || contract.useOrgPerson" type="success" size="small">
          <template #icon>
            <n-icon><i class="fa fa-user"></i></n-icon>
          </template>
          {{ contract.useOrgPersonName || `员工编码: ${contract.useOrgPerson}` }}
        </n-tag>
        <span v-else style="color: #999">未设置</span>
      </n-descriptions-item>
      
      <!-- 管理科室 -->
      <n-descriptions-item label="管理科室">
        <n-tag v-if="contract.manageOrgName || contract.manageOrg" type="warning" size="small">
          <template #icon>
            <n-icon><i class="fa fa-cogs"></i></n-icon>
          </template>
          {{ contract.manageOrgName || `科室代码: ${contract.manageOrg}` }}
        </n-tag>
        <span v-else style="color: #999">未设置</span>
      </n-descriptions-item>
      
      <!-- 管理科室负责人 -->
      <n-descriptions-item label="管理科室负责人">
        <n-tag v-if="contract.responsiblePersonName || contract.responsiblePerson" type="error" size="small">
          <template #icon>
            <n-icon><i class="fa fa-user-tie"></i></n-icon>
          </template>
          {{ contract.responsiblePersonName || `员工编码: ${contract.responsiblePerson}` }}
        </n-tag>
        <span v-else style="color: #999">未设置</span>
      </n-descriptions-item>
    </n-descriptions>

    <div class="debug-info" style="background: #f5f5f5; padding: 16px; border-radius: 8px; margin-top: 16px;">
      <h4>🔍 调试信息</h4>
      <pre>{{ JSON.stringify(contract, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { NDescriptions, NDescriptionsItem, NTag, NIcon } from 'naive-ui'

// 模拟您提供的实际数据
const contract = ref({
  id: 336,
  ctCode: "YJCGK-2025-CG-3 ",
  ctUnifiedCode: "JY-CGK-2025-0110", 
  ctName: "五金维修材料",
  useOrg: "521001",                    // 使用科室代码
  manageOrg: "531001",                 // 管理科室代码
  responsiblePerson: "0155",           // 管理科室负责人编码
  responsiblePersonName: "彭心梅",      // 管理科室负责人姓名 ✅ 已有
  // 注意：缺少 useOrgPerson, useOrgName, useOrgPersonName, manageOrgName
})
</script>

<style scoped>
.organization-info-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-info pre {
  font-size: 12px;
  color: #666;
  overflow-x: auto;
}
</style> 