<template>
  <div class="contract-payment-reim" style="padding: 16px">
    <j-title-line title="合同基本信息" style="margin-bottom: 16px" />
    <n-descriptions bordered :column="4" style="margin-bottom: 24px" label-placement="left">
      <n-descriptions-item label="合同名称">
        <strong>{{ props.contract.ctName }}</strong>
      </n-descriptions-item>
      <n-descriptions-item label="统一编号">
        <strong>{{ props.contract.ctUnifiedCode }}</strong>
      </n-descriptions-item>
      <n-descriptions-item label="合同编号">
        {{ props.contract.ctCode }}
      </n-descriptions-item>
      <n-descriptions-item label="付款相对方名称">
        <strong style="color: #18a058">{{ props.contract.oppositeName }}</strong>
      </n-descriptions-item>
      <n-descriptions-item label="相对方联系人">
        {{ props.contract.contactName }}
      </n-descriptions-item>
      <n-descriptions-item label="联系人方式">
        {{ props.contract.contactPhone }}
      </n-descriptions-item>
      <n-descriptions-item label="开户行">
        {{ props.contract.openingBank }}
      </n-descriptions-item>
      <n-descriptions-item label="银行账号">
        {{ props.contract.bankAccount }}
      </n-descriptions-item>
      <!-- 🆕 优化后的四个组织机构相关字段 -->
      <n-descriptions-item label="使用科室">
        <div v-if="props.contract.useOrg" class="org-info-container">
          <n-tag type="info" size="small" class="org-tag">
            <template #icon>
              <n-icon><i class="fa fa-building"></i></n-icon>
            </template>
            <span class="org-name">{{ getOrgDisplayName('useOrg') }}</span>
          </n-tag>
          <n-tooltip v-if="props.contract.useOrgName && props.contract.useOrg" trigger="hover">
            <template #trigger>
              <n-icon size="14" style="margin-left: 4px; color: #909399; cursor: help;">
                <i class="fa fa-info-circle"></i>
              </n-icon>
            </template>
            科室代码: {{ props.contract.useOrg }}
          </n-tooltip>
        </div>
        <span v-else style="color: #999; font-style: italic;">未设置</span>
      </n-descriptions-item>

      <n-descriptions-item label="使用科室负责人">
        <div v-if="props.contract.useOrgPerson" class="person-info-container">
          <n-tag type="success" size="small" class="person-tag">
            <template #icon>
              <n-icon><i class="fa fa-user"></i></n-icon>
            </template>
            <span class="person-name">{{ getPersonDisplayName('useOrgPerson') }}</span>
          </n-tag>
          <n-tooltip v-if="props.contract.useOrgPersonName && props.contract.useOrgPerson" trigger="hover">
            <template #trigger>
              <n-icon size="14" style="margin-left: 4px; color: #909399; cursor: help;">
                <i class="fa fa-info-circle"></i>
              </n-icon>
            </template>
            员工编码: {{ props.contract.useOrgPerson }}
          </n-tooltip>
        </div>
        <span v-else style="color: #999; font-style: italic;">未设置</span>
      </n-descriptions-item>

      <n-descriptions-item label="管理科室">
        <div v-if="props.contract.manageOrg" class="org-info-container">
          <n-tag type="warning" size="small" class="org-tag">
            <template #icon>
              <n-icon><i class="fa fa-cogs"></i></n-icon>
            </template>
            <span class="org-name">{{ getOrgDisplayName('manageOrg') }}</span>
          </n-tag>
          <n-tooltip v-if="props.contract.manageOrgName && props.contract.manageOrg" trigger="hover">
            <template #trigger>
              <n-icon size="14" style="margin-left: 4px; color: #909399; cursor: help;">
                <i class="fa fa-info-circle"></i>
              </n-icon>
            </template>
            科室代码: {{ props.contract.manageOrg }}
          </n-tooltip>
        </div>
        <span v-else style="color: #999; font-style: italic;">未设置</span>
      </n-descriptions-item>

      <n-descriptions-item label="管理科室负责人">
        <div v-if="props.contract.responsiblePerson" class="person-info-container">
          <n-tag type="error" size="small" class="person-tag">
            <template #icon>
              <n-icon><i class="fa fa-user-tie"></i></n-icon>
            </template>
            <span class="person-name">{{ getPersonDisplayName('responsiblePerson') }}</span>
          </n-tag>
          <n-tooltip v-if="props.contract.responsiblePersonName && props.contract.responsiblePerson" trigger="hover">
            <template #trigger>
              <n-icon size="14" style="margin-left: 4px; color: #909399; cursor: help;">
                <i class="fa fa-info-circle"></i>
              </n-icon>
            </template>
            员工编码: {{ props.contract.responsiblePerson }}
          </n-tooltip>
        </div>
        <span v-else style="color: #999; font-style: italic;">未设置</span>
      </n-descriptions-item>
    </n-descriptions>
    <div v-if="showPayTable">
      <j-title-line title="付款计划编制" class="exp-space">
        <template #default>
          <n-button type="info" strong secondary @click="savePayment">保存</n-button>
          <n-button type="warning" quaternary @click="closeEditPayment">取消编制</n-button>
        </template>
      </j-title-line>
      <n-form :model="editForm" label-width="80" style="width: 100%">
        <n-row :gutter="24">
          <n-col :span="12">
            <n-form-item label="合同总额" :show-require-mark="true" label-placement="left">
              <n-input-number
                :show-button="false"
                v-model:value="editForm.totalAmt"
                @update:value="priceInWord"
                :disabled="shouldDisableForm"
                style="width: 100%"
              ></n-input-number>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="合同总额大写" :show-require-mark="true" label-placement="left">
              <n-input
                :show-button="false"
                v-model:value="editForm.totalAmtInWord"
                style="width: 100%"
                :disabled="shouldDisableForm"
                readonly
              ></n-input>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="付款方式" :show-require-mark="true" label-placement="left">
              <j-select
                :dictType="'PAYMENT_TYPE'"
                v-model:value="editForm.paymentType"
                style="width: 100%"
                @update:value="onPaymentTypeChange"
              ></j-select>
            </n-form-item>
          </n-col>
          <n-col :span="12">
            <n-form-item label="付款期数" v-if="showPayTable" :show-require-mark="true" label-placement="left">
              <n-input-number
                :show-button="false"
                v-model:value="editForm.paymentTerms"
                disabled
                style="width: 100%"
              ></n-input-number>
            </n-form-item>
          </n-col>
        </n-row>
      </n-form>
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px">
        <div style="flex: 1; margin-right: 10px">
          <n-alert :type="calculateTotalProportion() >= 100 ? 'success' : 'info'" style="width: 100%">
            <template #icon>
              <n-icon><i class="fa fa-info-circle"></i></n-icon>
            </template>
            请填写完整的付款计划信息。已编制：{{ calculateTotalProportion() }}%（{{
              calculateTotalAmount()
            }}元），剩余：{{ 100 - calculateTotalProportion() }}%（{{
              editForm.totalAmt - calculateTotalAmount()
            }}元），合同总额：{{ editForm.totalAmt }}元
          </n-alert>
        </div>
        <div style="display: flex; gap: 8px">
          <n-button type="default" @click="showPaymentRequirementTerms" :disabled="hasNonDefaultStatusRows">
            <template #icon>
              <n-icon><i class="fa fa-list-alt"></i></n-icon>
            </template>
            付款要求词条
          </n-button>
          <n-button type="default" @click="showQuickProportionSetting" :disabled="hasNonDefaultStatusRows">
            <template #icon>
              <n-icon><i class="fa fa-percent"></i></n-icon>
            </template>
            快速设定比例
          </n-button>
          <n-button type="primary" @click="addNewRow">
            <template #icon>
              <n-icon><i class="fa fa-plus"></i></n-icon>
            </template>
            新增
          </n-button>
        </div>
      </div>
      <j-n-data-table
        v-if="showPayTable"
        ref="table"
        :columns="paymentColumns as any"
        :data="paymentData"
        :scroll-x="1500"
      >
      </j-n-data-table>

      <!-- 合同总金额分配分支动画 - 进度条样式 -->
      <div class="payment-amount-distribution" v-if="false && paymentData.length > 0" style="margin-top: 24px">
        <div class="timeline-title">合同总金额分配</div>

        <!-- 新增：总金额进度条展示 -->
        <div class="progress-bar-wrapper">
          <div class="progress-bar-container">
            <div
              v-for="(item, index) in paymentData"
              :key="index"
              class="progress-bar-segment"
              :style="{
                width: `${((Number(item.proportion) || 0) / calculateTotalProportion()) * 100}%`,
                backgroundColor: getAmountBlockColor(item),
              }"
              @click="showNodeDetail(item)"
            >
              <div class="progress-segment-tooltip">
                <div>{{ item.stage }}</div>
                <div>{{ item.totalAmt }}元 ({{ item.proportion }}%)</div>
              </div>
            </div>
          </div>

          <!-- 进度条下方的阶段标记 -->
          <div class="progress-bar-labels">
            <div
              v-for="(item, index) in paymentData"
              :key="index"
              class="progress-label"
              :style="{
                left: `${
                  (paymentData.slice(0, index).reduce((sum, i) => sum + (Number(i.proportion) || 0), 0) /
                    calculateTotalProportion()) *
                    100 +
                  (((Number(item.proportion) || 0) / calculateTotalProportion()) * 100) / 2
                }%`,
              }"
            >
              <div class="progress-label-marker" :style="{ backgroundColor: getAmountBlockColor(item) }"></div>
              <div class="progress-label-text">{{ item.stage }}</div>
            </div>
          </div>
        </div>

        <div class="amount-distribution-legend">
          <div class="legend-item">
            <div class="legend-color" style="background-color: #fcbd71"></div>
            <span>待报销</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #63b0ff"></div>
            <span>已生成报销任务</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #5cb87a"></div>
            <span>已报销</span>
          </div>
        </div>
      </div>

      <!-- 报销进度分支动画 -->
      <div class="reimbursement-progress-animation" v-if="paymentData.length > 0" style="margin-top: 24px">
        <div class="timeline-title">报销进度</div>

        <!-- 新增：水平步骤进度条 -->
        <div class="steps-progress-container">
          <div class="steps-line"></div>
          <div class="steps-items">
            <div
              v-for="(step, index) in reimbursementSteps"
              :key="index"
              class="step-item"
              :class="{
                'step-completed': hasCompletedStep(index),
                'step-current': isCurrentStep(index),
                'step-pending': isPendingStep(index),
              }"
            >
              <div class="step-circle">
                <span>{{ index + 1 }}</span>
              </div>
              <div class="step-label">{{ step.title }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
          </div>
        </div>

        <!-- 纵向多分支、横向状态流程图 -->
        <div class="flow-diagram-vertical">
          <!-- 源节点：待分配物资 -->
          <div class="flow-source-container">
            <div class="flow-node flow-source-node">
              <div class="flow-node-title">待报销物资</div>
              <div class="flow-node-count">{{ editForm.totalAmt }}元</div>
            </div>
          </div>

          <!-- 分支节点容器 -->
          <div class="flow-branches-container">
            <div v-for="(item, index) in paymentData" :key="index" class="flow-branch-vertical">
              <!-- 连接线 -->
              <div class="flow-connector-vertical">
                <div class="flow-line-vertical"></div>
                <div class="flow-arrow-vertical"></div>
              </div>

              <!-- 分支节点 -->
              <div class="flow-target-container">
                <div
                  class="flow-node flow-target-node"
                  :class="{ 'flow-node-active': item.reimStatusFlag }"
                  :style="{ backgroundColor: getNodeBackgroundColor(item) }"
                  @click="showNodeDetail(item)"
                >
                  <div class="flow-node-title">{{ item.stage }}</div>
                  <div class="flow-node-count">{{ item.totalAmt }}元</div>
                  <div class="flow-node-status">
                    {{ getReimStatusText(item) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 报销进度指示器 -->
        <div class="reimbursement-progress-indicator" style="margin-top: 16px">
          <n-progress
            type="line"
            :percentage="calculateReimbursementPercentage()"
            :processing="calculateReimbursementPercentage() > 0 && calculateReimbursementPercentage() < 100"
            :status="calculateReimbursementPercentage() === 100 ? 'success' : 'info'"
            :show-indicator="false"
          />
          <div class="progress-text">
            <span>已报销: {{ calculateReimbursedAmount() }}元</span>
            <span>待报销: {{ editForm.totalAmt - calculateReimbursedAmount() }}元</span>
            <span>完成率: {{ calculateReimbursementPercentage() }}%</span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!showPayTable" style="margin-top: 16px">
      <j-title-line title="付款报销计划" style="margin-bottom: 16px">
        <template #default>
          <n-button type="primary" strong secondary @click="openPayment">编制付款计划</n-button>
        </template>
      </j-title-line>
      <n-descriptions bordered :column="2" style="margin-bottom: 16px" label-placement="left">
        <n-descriptions-item label="合同总额">
          {{ editForm.totalAmt }}
        </n-descriptions-item>
        <n-descriptions-item label="合同总额大写">
          {{ editForm.totalAmtInWord }}
        </n-descriptions-item>
        <n-descriptions-item label="付款方式">
          <j-select :dictType="'PAYMENT_TYPE'" v-model:value="editForm.paymentType" disabled></j-select>
        </n-descriptions-item>
        <n-descriptions-item label="付款期数">
          {{ editForm.paymentTerms }}
        </n-descriptions-item>
      </n-descriptions>
      <j-n-data-table ref="table" :columns="waitPayReimColumns as any" :data="waitPayReimData" :scroll-x="1800">
      </j-n-data-table>

      <!-- 合同总金额分配分支动画 - 进度条样式 -->
      <div class="payment-amount-distribution" v-if="false && waitPayReimData.length > 0" style="margin-top: 24px">
        <div class="timeline-title">合同总金额分配</div>

        <!-- 新增：总金额进度条展示 -->
        <div class="progress-bar-wrapper">
          <div class="progress-bar-container">
            <div
              v-for="(item, index) in waitPayReimData"
              :key="index"
              class="progress-bar-segment"
              :style="{
                width: `${((Number(item.proportion) || 0) / calculateTotalReimProportion()) * 100}%`,
                backgroundColor: getAmountBlockColor(item),
              }"
              @click="showNodeDetail(item)"
            >
              <div class="progress-segment-tooltip">
                <div>{{ item.stage }}</div>
                <div>{{ item.totalAmt }}元 ({{ item.proportion }}%)</div>
              </div>
            </div>
          </div>

          <!-- 进度条下方的阶段标记 -->
          <div class="progress-bar-labels">
            <div
              v-for="(item, index) in waitPayReimData"
              :key="index"
              class="progress-label"
              :style="{
                left: `${
                  (waitPayReimData.slice(0, index).reduce((sum, i) => sum + (Number(i.proportion) || 0), 0) /
                    calculateTotalReimProportion()) *
                    100 +
                  (((Number(item.proportion) || 0) / calculateTotalReimProportion()) * 100) / 2
                }%`,
              }"
            >
              <div class="progress-label-marker" :style="{ backgroundColor: getAmountBlockColor(item) }"></div>
              <div class="progress-label-text">{{ item.stage }}</div>
            </div>
          </div>
        </div>

        <div class="amount-distribution-legend">
          <div class="legend-item">
            <div class="legend-color" style="background-color: #fcbd71"></div>
            <span>待报销</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #63b0ff"></div>
            <span>已生成报销任务</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #5cb87a"></div>
            <span>已报销</span>
          </div>
        </div>
      </div>

      <!-- 报销进度分支动画 -->
      <div class="reimbursement-progress-animation" v-if="waitPayReimData.length > 0" style="margin-top: 24px">
        <div class="timeline-title">报销进度</div>

        <!-- 新增：水平步骤进度条 -->
        <div class="steps-progress-container">
          <div class="steps-line"></div>
          <div class="steps-items">
            <div
              v-for="(step, index) in reimbursementSteps"
              :key="index"
              class="step-item"
              :class="{
                'step-completed': hasCompletedReimStep(index),
                'step-current': isCurrentReimStep(index),
                'step-pending': isPendingReimStep(index),
              }"
            >
              <div class="step-circle">
                <span>{{ index + 1 }}</span>
              </div>
              <div class="step-label">{{ step.title }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
          </div>
        </div>

        <!-- 纵向多分支、横向状态流程图 -->
        <div class="flow-diagram-vertical">
          <!-- 源节点：待分配物资 -->
          <div class="flow-source-container">
            <div class="flow-node flow-source-node">
              <div class="flow-node-title">待报销物资</div>
              <div class="flow-node-count">{{ editForm.totalAmt }}元</div>
            </div>
          </div>

          <!-- 分支节点容器 -->
          <div class="flow-branches-container">
            <div v-for="(item, index) in waitPayReimData" :key="index" class="flow-branch-vertical">
              <!-- 连接线 -->
              <div class="flow-connector-vertical">
                <div class="flow-line-vertical"></div>
                <div class="flow-arrow-vertical"></div>
              </div>

              <!-- 分支节点 -->
              <div class="flow-target-container">
                <div
                  class="flow-node flow-target-node"
                  :class="{ 'flow-node-active': item.reimStatusFlag }"
                  :style="{ backgroundColor: getNodeBackgroundColor(item) }"
                  @click="showNodeDetail(item)"
                >
                  <div class="flow-node-title">{{ item.stage }}</div>
                  <div class="flow-node-count">{{ item.totalAmt }}元</div>
                  <div class="flow-node-status">
                    {{ getReimStatusText(item) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 报销进度指示器 -->
        <div class="reimbursement-progress-indicator" style="margin-top: 16px">
          <n-progress
            type="line"
            :percentage="calculateReimReimbursementPercentage()"
            :processing="calculateReimReimbursementPercentage() > 0 && calculateReimReimbursementPercentage() < 100"
            :status="calculateReimReimbursementPercentage() === 100 ? 'success' : 'info'"
            :show-indicator="false"
          />
          <div class="progress-text">
            <span>已报销: {{ calculateReimReimbursedAmount() }}元</span>
            <span>待报销: {{ editForm.totalAmt - calculateReimReimbursedAmount() }}元</span>
            <span>完成率: {{ calculateReimReimbursementPercentage() }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 付款要求词条弹窗 -->
  <n-modal v-model:show="showPaymentRequirementModal" preset="card" title="常用付款要求词条" style="width: 600px">
    <n-card size="small" :bordered="false">
      <div style="margin-bottom: 16px">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px">
          <div style="font-weight: bold">操作模式：</div>
          <n-switch v-model:value="copyModeEnabled" size="small">
            <template #checked>复制模式</template>
            <template #unchecked>应用模式</template>
          </n-switch>
        </div>

        <div v-if="!copyModeEnabled" style="margin-bottom: 12px">
          <div style="font-weight: bold; margin-bottom: 4px">选择行：</div>
          <n-select
            v-model:value="selectedRowIndex"
            :options="
              paymentData.map((item, index) => ({
                label: `${index + 1}. ${item.stage}${item.reimStatusFlag ? ' (不可编辑)' : ''}`,
                value: index,
                disabled: !!item.reimStatusFlag,
              }))
            "
            placeholder="请选择要应用词条的行"
          />
        </div>

        <n-alert :type="copyModeEnabled ? 'info' : 'success'" style="margin-bottom: 12px">
          <template #icon>
            <n-icon><i class="fa fa-info-circle"></i></n-icon>
          </template>
          {{
            copyModeEnabled ? '复制模式：点击词条将复制到剪贴板，不会填入表格' : '应用模式：点击词条将直接填入选中行'
          }}
        </n-alert>
      </div>

      <n-divider style="margin: 12px 0" />

      <div style="max-height: 300px; overflow-y: auto">
        <n-list hoverable clickable>
          <n-list-item
            v-for="term in paymentRequirementTerms"
            :key="term.id"
            @click="() => handleTermClick(term.content)"
          >
            <div style="display: flex; align-items: center">
              <n-icon v-if="copyModeEnabled" style="margin-right: 8px"><i class="fa fa-copy"></i></n-icon>
              <n-icon v-else style="margin-right: 8px"><i class="fa fa-check"></i></n-icon>
              {{ term.content }}
            </div>
          </n-list-item>
        </n-list>
      </div>

      <template #footer>
        <div style="text-align: right">
          <n-button @click="showPaymentRequirementModal = false">关闭</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>

  <!-- 快速设定比例弹窗 -->
  <n-modal v-model:show="showProportionModal" preset="card" title="快速设定比例" style="width: 600px">
    <n-card title="预设比例方案" size="small">
      <div style="margin-bottom: 16px">
        <n-alert v-if="hasNonDefaultStatusRows" type="warning" style="margin-bottom: 12px">
          <template #icon>
            <n-icon><i class="fa fa-exclamation-circle"></i></n-icon>
          </template>
          提示：有 {{ paymentData.length - editableRows.length }} 条记录已有报销状态，这些记录将被跳过不进行修改
        </n-alert>

        <div style="font-weight: bold; margin-bottom: 8px">自定义比例：</div>
        <n-input v-model:value="customProportionInput" type="text" placeholder="输入数字，用空格分隔（如：1 2 7）" />
        <div style="font-size: 12px; color: #666; margin-top: 4px">
          输入简单数字，系统会自动计算比例。例如输入"1 2 7"，将设置为10%、20%、70%
        </div>

        <div style="margin-top: 12px; display: flex; gap: 8px">
          <n-button type="primary" size="small" @click="applyCustomProportion">应用自定义比例</n-button>
          <n-button size="small" @click="customProportionInput = ''">清空</n-button>
        </div>
      </div>
      <n-divider />
      <n-list hoverable clickable>
        <n-list-item v-for="preset in proportionPresets" :key="preset.id" @click="preset.apply">
          <div style="display: flex; flex-direction: column">
            <div style="font-weight: bold">{{ preset.name }}</div>
            <div style="font-size: 12px; color: #666">{{ preset.description }}</div>
          </div>
        </n-list-item>
      </n-list>
      <template #footer>
        <div style="text-align: right">
          <n-button @click="showProportionModal = false">关闭</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>

  <!-- 报销详细信息模态框 -->
  <n-modal v-model:show="showDetailModal" preset="card" title="付款阶段详细信息" style="width: 700px; max-width: 90vw">
    <n-card v-if="selectedNode" title="付款详情" :bordered="false">
      <!-- 添加状态步骤条 -->
      <div class="reimbursement-status-steps">
        <div class="status-steps-title">报销进度状态</div>
        <div class="status-steps-container">
          <div
            v-for="(step, index) in reimbursementSteps"
            :key="index"
            class="status-step"
            :class="{
              'status-step-completed': getStepStatus(selectedNode, step.value) === 'completed',
              'status-step-current': getStepStatus(selectedNode, step.value) === 'current',
              'status-step-pending': getStepStatus(selectedNode, step.value) === 'pending',
            }"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
            <div class="step-connector" v-if="index < reimbursementSteps.length - 1"></div>
          </div>
        </div>
      </div>

      <n-descriptions bordered :column="2" label-placement="left">
        <n-descriptions-item label="付款阶段" :span="2">
          <n-tag :type="getTimelineItemType(selectedNode)">{{ selectedNode.stage }}</n-tag>
        </n-descriptions-item>

        <n-descriptions-item label="比例"> {{ selectedNode.proportion }}% </n-descriptions-item>

        <n-descriptions-item label="付款金额">
          <span style="font-weight: bold; color: #18a058">{{ selectedNode.totalAmt }}元</span>
        </n-descriptions-item>

        <n-descriptions-item label="付款要求" :span="2">
          {{ selectedNode.requirements || '未设置' }}
        </n-descriptions-item>

        <n-descriptions-item label="预计付款时间">
          {{ selectedNode.paymentTime || '未设置' }}
        </n-descriptions-item>

        <n-descriptions-item label="报销状态">
          <n-tag :type="getTimelineItemType(selectedNode)" :bordered="!!selectedNode.actualPaymentTime">
            {{ getReimStatusText(selectedNode) }}
          </n-tag>
        </n-descriptions-item>
      </n-descriptions>

      <div v-if="hasPushTaskOrReimburse" style="margin-top: 16px">
        <n-divider>报销信息</n-divider>
        <n-descriptions bordered :column="2" label-placement="left">
          <n-descriptions-item label="推送任务时间">
            {{ selectedNode.pushTaskTime || '-' }}
          </n-descriptions-item>

          <n-descriptions-item label="报销单号">
            {{ selectedNode.reimburseNo || '-' }}
          </n-descriptions-item>

          <n-descriptions-item label="报销人">
            {{ selectedNode.reimbursePerson || '-' }}
          </n-descriptions-item>

          <n-descriptions-item label="报销部门">
            {{ selectedNode.reimburseDept || '-' }}
          </n-descriptions-item>

          <n-descriptions-item label="报销时间">
            {{ selectedNode.reimburseTime || '-' }}
          </n-descriptions-item>

          <n-descriptions-item label="实际付款时间">
            <span v-if="selectedNode.actualPaymentTime" style="font-weight: bold; color: #18a058">
              {{ selectedNode.actualPaymentTime }}
            </span>
            <span v-else>-</span>
          </n-descriptions-item>
        </n-descriptions>
      </div>

      <div style="margin-top: 16px; display: flex; justify-content: flex-end">
        <n-button v-if="!selectedNode.pushTaskTime" type="info" @click="goGenerateWaitReimApply(selectedNode)">
          生成报销任务
        </n-button>
        <n-button type="primary" style="margin-left: 8px" @click="goEcsContractReim(selectedNode)"> 跳转报销 </n-button>
      </div>
    </n-card>
  </n-modal>
</template>

<style scoped>
/* 🆕 组织机构信息显示样式 */
.org-info-container, .person-info-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.org-tag, .person-tag {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.org-tag:hover, .person-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.org-name, .person-name {
  font-size: 13px;
  font-weight: 500;
}

/* 信息提示图标样式 */
.fa-info-circle {
  transition: color 0.2s ease;
}

.fa-info-circle:hover {
  color: #409eff !important;
}

/* 未设置状态样式 */
span[style*="color: #999"] {
  font-size: 12px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}
</style>

<script setup lang="ts">
  import { ref, reactive, watch, onMounted, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import JPGlobal from '@jutil'
  import {
    NDatePicker,
    NInput,
    NInputNumber,
    NDescriptions,
    NDescriptionsItem,
    NTag,
    NIcon,
    NButton,
    NAlert,
    NTimeline,
    NTimelineItem,
    NModal,
    NCard,
    NList,
    NListItem,
    NDivider,
    NSelect,
    NSwitch,
  } from 'naive-ui'
  import { CRUDColumnInterface } from '@/types/comps/crud.ts'
  import { queryCmsPaymentTerms, updatePaymentTerms, goGenerateReim } from '@/api/cms/contractManage/PaymentTermsWeb.ts'
  import { queryDictData } from '@/api/hrm/dictManage/treeSelectDict'
  // 🆕 新增导入：组织机构和员工查询API
  import { queryOrg } from '@/api/hrm/hrmOrg'
  import { queryEmployeeList } from '@/api/hrm/hrmEmp'

  const router = useRouter()

  // 付款方式字典数据
  const paymentTypeDict = ref([])

  // 🆕 新增：组织机构和员工信息的响应式数据
  const useOrgInfo = ref({ name: '', code: '' })
  const useOrgPersonInfo = ref({ name: '', code: '' })
  const manageOrgInfo = ref({ name: '', code: '' })
  const responsiblePersonInfo = ref({ name: '', code: '' })

  // 查询付款方式字典
  const queryPaymentTypeDict = async () => {
    try {
      const res = await queryDictData({ codeType: 'PAYMENT_TYPE' })
      paymentTypeDict.value = res.data
      console.log('付款方式字典:', paymentTypeDict.value)
    } catch (error) {
      console.error('查询付款方式字典失败:', error)
    }
  }

  // 🆕 新增：查询组织机构信息
  const queryOrgInfo = async (orgCode: string) => {
    if (!orgCode) return { name: '', code: '' }
    try {
      const res = await queryOrg({ orgCode })
      if (res.code === '200' && res.data && res.data.length > 0) {
        return {
          name: res.data[0].orgName || '',
          code: orgCode
        }
      }
    } catch (error) {
      console.error('查询组织机构信息失败:', error)
    }
    return { name: '', code: orgCode }
  }

  // 🆕 新增：查询员工信息
  const queryEmployeeInfo = async (empCode: string) => {
    if (!empCode) return { name: '', code: '' }
    try {
      const res = await queryEmployeeList({ empCode })
      if (res.code === '200' && res.data && res.data.length > 0) {
        return {
          name: res.data[0].empName || '',
          code: empCode
        }
      }
    } catch (error) {
      console.error('查询员工信息失败:', error)
    }
    return { name: '', code: empCode }
  }

  // 🆕 新增：加载组织机构和员工信息
  const loadOrgAndEmployeeInfo = async () => {
    try {
      // 🔧 新的实现：批量获取组织机构和员工信息
      const orgCodes = [props.contract.useOrg, props.contract.manageOrg].filter(Boolean)
      const empCodes = [props.contract.useOrgPerson, props.contract.responsiblePerson].filter(Boolean)

      // 并行获取组织机构和员工信息
      await Promise.all([
        fetchOrgInfo(orgCodes),
        fetchPersonInfo(empCodes)
      ])

      console.log('组织机构信息缓存:', orgInfoCache.value)
      console.log('员工信息缓存:', personInfoCache.value)
    } catch (error) {
      console.error('加载组织机构和员工信息失败:', error)
    }
  }

  // 🆕 新增：获取科室显示名称的方法
  const getOrgDisplayName = (orgField: string) => {
    const orgCode = props.contract[orgField]
    const orgNameField = orgField + 'Name'
    const orgName = props.contract[orgNameField]

    if (orgName) {
      return orgName
    } else if (orgCode) {
      // 🔧 临时解决方案：如果后端没有返回名称，尝试从本地缓存获取
      const cachedOrgName = orgInfoCache.value[orgCode]
      if (cachedOrgName) {
        return cachedOrgName
      }
      return `科室代码: ${orgCode}`
    }
    return '未知科室'
  }

  // 🆕 新增：获取员工显示名称的方法
  const getPersonDisplayName = (personField: string) => {
    const personCode = props.contract[personField]
    const personNameField = personField + 'Name'
    const personName = props.contract[personNameField]

    if (personName) {
      return personName
    } else if (personCode) {
      // 🔧 临时解决方案：如果后端没有返回名称，尝试从本地缓存获取
      const cachedPersonName = personInfoCache.value[personCode]
      if (cachedPersonName) {
        return cachedPersonName
      }
      return `员工编码: ${personCode}`
    }
    return '未知员工'
  }

  // 🆕 新增：组织机构信息缓存
  const orgInfoCache = ref<Record<string, string>>({})
  const personInfoCache = ref<Record<string, string>>({})

  // 🆕 新增：批量获取组织机构信息
  const fetchOrgInfo = async (orgCodes: string[]) => {
    if (orgCodes.length === 0) return

    try {
      // 调用组织机构查询API
      const response = await queryOrg({
        orgIds: orgCodes.join(','),
        hospitalId: 'zjxrmyy'
      })

      if (response.data && Array.isArray(response.data)) {
        response.data.forEach((org: any) => {
          if (org.orgId && org.orgName) {
            orgInfoCache.value[org.orgId] = org.orgName
          }
          if (org.orgCode && org.orgName) {
            orgInfoCache.value[org.orgCode] = org.orgName
          }
        })
      }
    } catch (error) {
      console.error('获取组织机构信息失败:', error)
    }
  }

  // 🆕 新增：批量获取员工信息
  const fetchPersonInfo = async (empCodes: string[]) => {
    if (empCodes.length === 0) return

    try {
      // 这里需要根据实际的员工查询API进行调用
      // 暂时使用组织机构API，您可能需要调整为实际的员工查询API
      const response = await queryOrg({
        empCodes: empCodes.join(','),
        hospitalId: 'zjxrmyy'
      })

      if (response.data && Array.isArray(response.data)) {
        response.data.forEach((emp: any) => {
          if (emp.empCode && emp.empName) {
            personInfoCache.value[emp.empCode] = emp.empName
          }
        })
      }
    } catch (error) {
      console.error('获取员工信息失败:', error)
    }
  }

  const props = defineProps({
    payMentData: {
      type: Array,
      default: () => [],
    },
    contractInfo: {
      type: Object,
      default: () => ({
        contractName: '',
        contractCode: '',
        ctUnifiedCode: '',
        partyName: '',
        partyContact: '',
        contactPhone: '',
        openingBank: '',
        bankAccount: '',
      }),
    },
    // 合同基本信息
    contract: {
      type: Object,
      default: () => ({
        id: '',
        ctName: '', // 合同名称
        ctCode: '', // 合同编号
        ctUnifiedCode: '', // 统一编号
        oppositeName: '', // 付款相对方名称
        contactName: '', // 相对方联系人
        contactPhone: '', // 联系人方式
        openingBank: '', // 开户行
        bankAccount: '', // 银行账号
        totalAmt: 0, // 合同总额
        paymentType: '', // 付款方式
        paymentTerms: 0, // 付款期数
        // 🆕 新增的组织机构相关字段
        useOrg: '', // 使用科室代码
        useOrgPerson: '', // 使用科室负责人员工编码
        manageOrg: '', // 管理科室代码
        responsiblePerson: '', // 管理科室负责人员工编码
        // 🆕 后端可能返回的名称字段
        useOrgName: '', // 使用科室名称
        useOrgPersonName: '', // 使用科室负责人姓名
        manageOrgName: '', // 管理科室名称
        responsiblePersonName: '', // 管理科室负责人姓名
      }),
    },
  })

  const table = ref()
  const showPayTable = ref(false)
  const editForm = reactive({
    totalAmt: 0,
    totalAmtInWord: '',
    paymentType: '',
    paymentTerms: 0,
  })

  // 添加计算属性来检查是否应该禁用表单
  const shouldDisableForm = computed(() => {
    return paymentData.value.some(item => !!item.reimStatusFlag)
  })

  const paymentData = ref([])

  const priceInWord = () => {
    editForm.totalAmtInWord = JPGlobal.numberToChineseCapital(editForm.totalAmt)
  }

  // 根据付款方式和索引生成付款阶段名称
  const generateStageNameByPaymentType = (paymentType: string, index: number, totalTerms: number) => {
    // 默认的付款阶段名称（分期付款和一次性付款）
    let stageName = index === 0 ? '预付款' : index === totalTerms - 1 ? '尾款' : '进度款'

    // 根据不同的付款方式生成不同的付款阶段名称
    switch (paymentType) {
      case '3': // 分批付款
        stageName = `第${index + 1}批`
        break
      case '4': // 按年支付
        stageName = `第${index + 1}年支付`
        break
      case '5': // 据实支付
        stageName = `第${index + 1}次支付`
        break
      // 分期付款和一次性付款保持原样
      case '1': // 一次性付款
      case '2': // 分期付款
      default:
        // 使用默认值
        break
    }

    return stageName
  }

  const selectPaymentTerms = (value: string) => {
    switch (value) {
      case '2':
        //分期付款
        editForm.paymentTerms = 3
        break
      case '1':
        //一次性付款
        editForm.paymentTerms = 1
        break
      case '3':
        //分批付款
        editForm.paymentTerms = 3
        break
      case '4':
        //按年支付
        editForm.paymentTerms = 3
        break
      case '5':
        //据实支付
        editForm.paymentTerms = 3
        break
      default:
        editForm.paymentTerms = 3
        break
    }

    // 根据付款方式和期数更新表格数据
    updatePaymentDataBasedOnTerms()
  }

  // 根据付款期数更新表格数据
  const updatePaymentDataBasedOnTerms = () => {
    if (editForm.paymentTerms === 0) {
      showPayTable.value = false
      paymentData.value = []
    } else {
      showPayTable.value = true

      // 保存已有报销状态的记录
      const reimStatusRecords = paymentData.value.filter(item => !!item.reimStatusFlag)

      // 清空数组
      while (paymentData.value.length > 0) {
        paymentData.value.pop()
      }

      // 首先添加回已有报销状态的记录
      reimStatusRecords.forEach(record => {
        paymentData.value.push(record)
      })

      // 计算需要添加的新记录数量
      const newRecordsNeeded = editForm.paymentTerms - reimStatusRecords.length

      // 如果需要添加新记录
      if (newRecordsNeeded > 0) {
        for (let i = 0; i < newRecordsNeeded; i++) {
          // 创建一个新的vo对象并添加到数组中
          const currentIndex = paymentData.value.length
          let newVo = {
            stage: generateStageNameByPaymentType(editForm.paymentType, currentIndex, editForm.paymentTerms),
            requirements: ``,
            proportion: ``,
            totalAmt: ``,
            paymentTime: ``,
            seq: currentIndex + 1,
          }
          paymentData.value.push(newVo)
        }
      }

      // 更新每个元素的seq属性
      paymentData.value.forEach((item, index) => {
        item.seq = index + 1
        // 如果没有报销状态，则更新付款阶段名称
        if (!item.reimStatusFlag) {
          item.stage = generateStageNameByPaymentType(editForm.paymentType, index, editForm.paymentTerms)
        }
      })

      // 确保付款类型与期数保持一致（仅对一次性付款和分期付款进行处理）
      if (paymentData.value.length > 1 && editForm.paymentType === '1') {
        editForm.paymentType = '2' // 更新为分期付款
      } else if (paymentData.value.length === 1 && editForm.paymentType === '2') {
        editForm.paymentType = '1' // 更新为一次性付款
      }
      // 其他付款方式（分批付款、按年支付、据实支付）不需要特殊处理，保持原样
    }
  }

  // 查询付款详情数据
  const queryPaymentDetails = async (contractId: string) => {
    try {
      if (!contractId) {
        console.warn('合同ID为空，无法查询付款详情')
        return
      }
      console.log('开始查询付款详情，合同ID:', contractId)
      const res = await queryCmsPaymentTerms({ contractId })
      console.log('付款详情查询结果:', res.data)

      // 更新付款计划编制数据
      paymentData.value = res.data

      // 同步更新付款报销计划表格数据
      waitPayReimData.value = res.data

      // 如果有数据，更新编辑表单的值
      if (res.data && res.data.length > 0) {
        // 从合同中获取总额和付款方式
        if (props.contract?.totalAmt) {
          editForm.totalAmt = props.contract.totalAmt
          priceInWord()
        }

        if (props.contract?.paymentType) {
          editForm.paymentType = props.contract.paymentType
        }

        // 设置付款期数为实际数据的长度
        editForm.paymentTerms = res.data.length
      }
    } catch (error) {
      console.error('查询付款详情失败:', error)
    }
  }

  onMounted(async () => {
    // 查询付款方式字典
    await queryPaymentTypeDict()

    // 打印传入的参数
    console.log('合同基本信息:', props.contract)
    console.log('付款计划数据:', props.payMentData)

    // 初始化数据
    paymentData.value = props.payMentData

    // 设置合同总额
    if (props.contract?.totalAmt) {
      editForm.totalAmt = props.contract.totalAmt
      priceInWord()
    }

    // 设置付款方式和付款期数
    if (props.contract?.paymentType) {
      editForm.paymentType = props.contract.paymentType
      // 如果合同中有付款期数，则使用合同中的值
      if (props.contract?.paymentTerms) {
        editForm.paymentTerms = props.contract.paymentTerms
      } else {
        // 否则根据付款方式设置默认的付款期数
        selectPaymentTerms(props.contract.paymentType)
      }
    }

    // 查询付款详情
    if (props.contract?.id) {
      await queryPaymentDetails(props.contract.id)
    }

    // �� 新增：加载组织机构和员工信息
    loadOrgAndEmployeeInfo()
  })

  const renderReimStatus = (row: any) => {
    let text = ''
    let type: 'warning' | 'success' | 'info' = 'warning'
    switch (row.reimStatusFlag) {
      case '0':
        text = '已生成待报销任务'
        type = 'info'
        break
      case '1':
        text = '已生成报销任务'
        type = 'info'
        break
      case '2':
        text = '已报销'
        type = 'success'
        break
      default:
        text = '待报销'
        type = 'warning'
    }
    return h(NTag, { type }, { default: () => text })
  }

  const renderReimStatusNew = (row: any) => {
    let text = ''
    let type: 'warning' | 'info' | 'success' = 'warning'
    let bordered = false
    let style = {} // 用于添加边框等样式

    // 4. 已付款 (最高优先级)
    if (row.actualPaymentTime) {
      text = '已付款'
      type = 'success'
      bordered = true // 添加边框修饰
      style = { border: '1px solid #5cb87a', fontWeight: 'bold' } // 示例边框样式
    }
    // 3. 已提交报销，报销中
    else if (row.reimId && row.reimburseTime) {
      text = '已提交报销，报销中'
      type = 'success'
    }
    // 2. 待提交报销申请
    else if (row.pushTaskTime && !row.reimId) {
      text = '待提交报销申请'
      type = 'info'
    }
    // 1. 待推送报销任务
    else if (!row.pushTaskTime) {
      text = '待推送报销任务'
      type = 'warning'
    }
    // 默认状态（原报销审批中）
    else {
      text = '原报销审批中' // <--- 修改在这里
      type = 'warning' // 保持 warning 类型或者根据需要调整
    }

    return h(NTag, { type, bordered, style }, { default: () => text })
  }

  // 判断行是否处于非默认状态（不可编辑状态）
  const isRowNotDefaultState = (row: any) => {
    return (
      !!row.reimStatusFlag || // 任何报销状态标记
      !!row.actualPaymentTime || // 已付款
      !!(row.reimId && row.reimburseTime) || // 已提交报销，报销中
      !!(row.pushTaskTime && !row.reimId) // 待提交报销申请
    )
  }

  const paymentColumns = ref<CRUDColumnInterface[]>([
    {
      title: '付款阶段',
      key: 'stage',
      width: 100,
      render: (row: any) => {
        return h(
          NInput,
          {
            value: row.stage,
            disabled: isRowNotDefaultState(row),
            onUpdateValue: (val: string | null) => {
              // 再次验证是否可编辑，确保安全
              if (val && !isRowNotDefaultState(row)) {
                row.stage = val
              } else if (isRowNotDefaultState(row)) {
                window.$message.error('该行已有报销状态，不能修改付款阶段')
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '付款要求',
      key: 'requirements',
      width: 200,
      render: (row: any) => {
        return h(
          NInput,
          {
            value: row.requirements,
            disabled: isRowNotDefaultState(row),
            onUpdateValue: (val: string | null) => {
              // 再次验证是否可编辑，确保安全
              if (val && !isRowNotDefaultState(row)) {
                row.requirements = val
              } else if (isRowNotDefaultState(row)) {
                window.$message.error('该行已有报销状态，不能修改付款要求')
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '比例',
      key: 'proportion',
      width: 80,
      render: (row: any) => {
        return h(
          NInputNumber,
          {
            showButton: false,
            value: row.proportion,
            disabled: isRowNotDefaultState(row),
            onUpdateValue: (val: number | null) => {
              // 再次验证是否可编辑，确保安全
              if (val && !isRowNotDefaultState(row)) {
                row.proportion = val
                // 根据比例计算当期付款总额
                row.totalAmt = (val / 100) * editForm.totalAmt
              } else if (isRowNotDefaultState(row)) {
                window.$message.error('该行已有报销状态，不能修改付款金额')
              } else if (!val) {
                window.$message.error('请完善付款信息')
              }
            },
          },
          {
            suffix: () => '%',
          }
        )
      },
    },
    {
      title: '当期付款总额',
      key: 'totalAmt',
      width: 100,
      render: (row: any) => {
        return h(
          NInputNumber,
          {
            value: row.totalAmt,
            showButton: false,
            disabled: isRowNotDefaultState(row),
            onUpdateValue: (val: number | null) => {
              // 再次验证是否可编辑，确保安全
              if (val && !isRowNotDefaultState(row)) {
                row.totalAmt = val
                // 根据当期付款总额计算比例
                if (editForm.totalAmt > 0) {
                  row.proportion = (val / editForm.totalAmt) * 100
                }
              } else if (isRowNotDefaultState(row)) {
                window.$message.error('该行已有报销状态，不能修改付款金额')
              }
            },
          },
          {
            suffix: () => '元',
          }
        )
      },
    },
    {
      title: '预计付款时间',
      key: 'paymentTime',
      width: 100,
      render: (row: any) => {
        return h(
          NDatePicker,
          {
            type: 'date',
            valueFormat: 'yyyy-MM-dd',
            disabled: isRowNotDefaultState(row),
            onUpdateFormattedValue: (val: string | null) => {
              // 再次验证是否可编辑，确保安全
              if (val && !isRowNotDefaultState(row)) {
                row.paymentTime = val
              } else if (isRowNotDefaultState(row)) {
                window.$message.error('该行已有报销状态，不能修改付款时间')
              } else if (!val) {
                window.$message.error('请完善付款信息')
              }
            },
          },
          {}
        )
      },
    },
    {
      title: '报销状态',
      key: 'reimStatus',
      width: 180,
      render: renderReimStatusNew,
    },
    {
      title: '操作',
      key: 'operation',
      width: 80,
      fixed: 'right',
      render: (row: any) => {
        return h(
          NButton,
          {
            type: 'error',
            size: 'small',
            disabled: isRowNotDefaultState(row),
            onClick: () => deleteRow(row),
          },
          { default: () => '删除' }
        )
      },
    },
  ])

  // 计算属性：可编辑的行（没有报销状态的行）
  const editableRows = computed(() => {
    return paymentData.value.filter(item => !item.reimStatusFlag)
  })

  // 打开编制付款计划
  const openPayment = () => {
    // 设置合同总额
    if (props.contract?.totalAmt) {
      editForm.totalAmt = props.contract.totalAmt
      priceInWord()
    }

    // 设置付款方式和付款期数
    if (props.contract?.paymentType) {
      editForm.paymentType = props.contract.paymentType
    }

    // 如果已有付款数据，则使用已有数据的期数
    if (paymentData.value && paymentData.value.length > 0) {
      editForm.paymentTerms = paymentData.value.length
    } else {
      // 否则根据付款方式设置默认的付款期数
      selectPaymentTerms(editForm.paymentType)
    }

    // 显示付款计划编制，隐藏付款报销计划
    showPayTable.value = true
  }

  //取消编制
  const closeEditPayment = async () => {
    // 隐藏付款计划编制，显示付款报销计划
    showPayTable.value = false

    // 直接刷新数据，不做任何条件判断
    await queryPaymentDetails(props.contract.id)
    dataChanged.value = false
  }
  //保存付款计划
  const savePayment = async () => {
    try {
      // 校验必填项
      const hasEmptyFields = paymentData.value.some(item => {
        return !item.totalAmt
      })
      if (hasEmptyFields) {
        window.$message.error('请完善所有付款信息')
        return
      }

      // 确保付款类型与期数保持一致
      if (paymentData.value.length > 1 && editForm.paymentType === '1') {
        editForm.paymentType = '2' // 更新为分期付款
      } else if (paymentData.value.length === 1 && editForm.paymentType === '2') {
        editForm.paymentType = '1' // 更新为一次性付款
      }

      // 获取原始数据，用于检查已有报销状态的记录
      const originalData = await queryCmsPaymentTerms({ contractId: props.contract.id })
      const originalReimStatusRecords = originalData.data.filter(item => !!item.reimStatusFlag)

      // 检查每个有报销状态的记录是否在当前数据中保持不变
      for (const originalRecord of originalReimStatusRecords) {
        const currentRecord = paymentData.value.find(item => item.id === originalRecord.id)
        if (!currentRecord) {
          window.$message.error('不能删除已有报销状态的记录')
          return
        }

        // 检查记录的关键字段是否被修改
        if (
          currentRecord.stage !== originalRecord.stage ||
          currentRecord.requirements !== originalRecord.requirements ||
          currentRecord.proportion !== originalRecord.proportion ||
          currentRecord.totalAmt !== originalRecord.totalAmt ||
          currentRecord.paymentTime !== originalRecord.paymentTime
        ) {
          window.$message.error('不能修改已有报销状态的记录')
          return
        }
      }

      // 构造保存参数
      const params = {
        contractId: props.contract.id,
        totalAmt: editForm.totalAmt,
        paymentType: editForm.paymentType,
        paymentTerms: editForm.paymentTerms,
        paymentList: paymentData.value,
      }

      // 调用保存API
      const res = await updatePaymentTerms(params)
      if (res.code === '200') {
        window.$message.success('保存成功')

        // 隐藏付款计划编制，显示付款报销计划
        showPayTable.value = false

        // 保存成功后，直接使用当前内存中的数据更新waitPayReimData，不重新查询
        waitPayReimData.value = JSON.parse(JSON.stringify(paymentData.value))

        // 标记数据已变更，需要在操作前刷新
        dataChanged.value = true
      }
    } catch (error) {
      console.error('保存付款计划失败:', error)
      window.$message.error('保存失败')
    }
  }

  let waitPayReimData = ref([])
  //待报销记录
  const waitPayReimColumns = ref<CRUDColumnInterface[]>([
    {
      title: '付款阶段',
      key: 'stage',
      width: 100,
      render: (row: any) => {
        return h('span', null, row.stage)
      },
    },
    {
      title: '付款要求',
      key: 'requirements',
      width: 200,
      render: (row: any) => {
        return h('span', null, row.requirements)
      },
    },
    {
      title: '比例',
      key: 'proportion',
      width: 80,
      render: (row: any) => {
        return h('span', null, `${row.proportion}%`)
      },
    },
    {
      title: '当期付款总额',
      key: 'totalAmt',
      width: 100,
      render: (row: any) => {
        return h('span', null, `${row.totalAmt}元`)
      },
    },
    {
      title: '预计付款时间',
      key: 'paymentTime',
      width: 100,
      render: (row: any) => {
        return h('span', null, row.paymentTime)
      },
    },
    {
      title: '报销状态',
      key: 'reimStatus',
      width: 180,
      render: renderReimStatusNew,
    },
    {
      title: '实际付款时间',
      key: 'actualPaymentTime',
      width: 120,
      render: (row: any) => {
        return h('span', null, row.actualPaymentTime || '-')
      },
    },
    {
      title: '报销人',
      key: 'reimbursePerson',
      width: 100,
      render: (row: any) => {
        return h('span', null, row.reimbursePerson || '-')
      },
    },
    {
      title: '报销部门',
      key: 'reimburseDept',
      width: 120,
      render: (row: any) => {
        return h('span', null, row.reimburseDept || '-')
      },
    },
    {
      title: '报销时间',
      key: 'reimburseTime',
      width: 120,
      render: (row: any) => {
        return h('span', null, row.reimburseTime || '-')
      },
    },
    {
      title: '报销单号',
      key: 'reimburseNo',
      width: 120,
      render: (row: any) => {
        return h('span', null, row.reimburseNo || '-')
      },
    },
    {
      title: '推送任务时间',
      key: 'pushTaskTime',
      width: 120,
      render: (row: any) => {
        return h('span', null, row.pushTaskTime || '-')
      },
    },
    {
      title: '操作',
      key: 'operation',
      width: 200,
      fixed: 'right',
      render: (row: any) => {
        return h('div', { style: 'display: flex; gap: 8px; align-items: center;' }, [
          // 只有当没有推送任务时间时，才显示"生成报销任务"按钮
          !row.pushTaskTime
            ? h(
                NTag,
                {
                  type: 'info',
                  style: 'cursor: pointer',
                  onClick: () => {
                    goGenerateWaitReimApply(row)
                  },
                },
                { default: () => '生成报销任务' }
              )
            : null,
          h(
            NTag,
            {
              type: 'success',
              style: 'cursor: pointer',
              onClick: () => {
                goEcsContractReim(row)
              },
            },
            { default: () => '跳转报销' }
          ),
          // 当有推送任务时间时，显示"已推送报销任务"提示
          row.pushTaskTime
            ? h('span', { style: 'color: #909399; font-size: 12px; margin-left: 5px;' }, '已推送报销任务')
            : null,
        ])
      },
    },
    {
      title: '状态流程',
      key: 'statusFlow',
      width: 240,
      render: (row: any) => {
        return h('div', { class: 'status-flow-indicators' }, [
          // 1. 待推送报销任务
          h(
            'div',
            {
              class: ['status-indicator', !row.pushTaskTime ? 'status-current' : 'status-completed'],
              style: {
                backgroundColor: !row.pushTaskTime ? '#fcbd71' : '#5cb87a',
              },
              'data-tooltip': '待推送报销任务',
            },
            [h(NIcon, {}, { default: () => h('i', { class: !row.pushTaskTime ? 'fa fa-clock-o' : 'fa fa-check' }) })]
          ),

          // 连接线
          h('div', { class: 'status-connector' }),

          // 2. 已推送报销任务
          h(
            'div',
            {
              class: [
                'status-indicator',
                !row.pushTaskTime
                  ? 'status-pending'
                  : row.pushTaskTime && !row.reimId
                  ? 'status-current'
                  : 'status-completed',
              ],
              style: {
                backgroundColor: !row.pushTaskTime
                  ? '#f5f5f5'
                  : row.pushTaskTime && !row.reimId
                  ? '#63b0ff'
                  : '#5cb87a',
              },
              'data-tooltip': '已推送报销任务',
            },
            [
              h(
                NIcon,
                {},
                {
                  default: () =>
                    h('i', {
                      class: !row.pushTaskTime
                        ? 'fa fa-circle-o'
                        : row.pushTaskTime && !row.reimId
                        ? 'fa fa-refresh'
                        : 'fa fa-check',
                    }),
                }
              ),
            ]
          ),

          // 连接线
          h('div', { class: 'status-connector' }),

          // 3. 已提交报销申请
          h(
            'div',
            {
              class: [
                'status-indicator',
                !row.reimId
                  ? 'status-pending'
                  : row.reimId && !row.reimburseTime
                  ? 'status-current'
                  : 'status-completed',
              ],
              style: {
                backgroundColor: !row.reimId ? '#f5f5f5' : row.reimId && !row.reimburseTime ? '#63b0ff' : '#5cb87a',
              },
              'data-tooltip': '已提交报销申请',
            },
            [
              h(
                NIcon,
                {},
                {
                  default: () =>
                    h('i', {
                      class: !row.reimId
                        ? 'fa fa-circle-o'
                        : row.reimId && !row.reimburseTime
                        ? 'fa fa-refresh'
                        : 'fa fa-check',
                    }),
                }
              ),
            ]
          ),

          // 连接线
          h('div', { class: 'status-connector' }),

          // 4. 报销审批中
          h(
            'div',
            {
              class: [
                'status-indicator',
                !row.reimId || !row.reimburseTime
                  ? 'status-pending'
                  : row.reimId && row.reimburseTime && !row.actualPaymentTime
                  ? 'status-current'
                  : 'status-completed',
              ],
              style: {
                backgroundColor:
                  !row.reimId || !row.reimburseTime
                    ? '#f5f5f5'
                    : row.reimId && row.reimburseTime && !row.actualPaymentTime
                    ? '#63b0ff'
                    : '#5cb87a',
              },
              'data-tooltip': '报销审批中',
            },
            [
              h(
                NIcon,
                {},
                {
                  default: () =>
                    h('i', {
                      class:
                        !row.reimId || !row.reimburseTime
                          ? 'fa fa-circle-o'
                          : row.reimId && row.reimburseTime && !row.actualPaymentTime
                          ? 'fa fa-refresh'
                          : 'fa fa-check',
                    }),
                }
              ),
            ]
          ),

          // 连接线
          h('div', { class: 'status-connector' }),

          // 5. 已完成付款
          h(
            'div',
            {
              class: ['status-indicator', !row.actualPaymentTime ? 'status-pending' : 'status-current'],
              style: {
                backgroundColor: !row.actualPaymentTime ? '#f5f5f5' : '#2b9f5e',
              },
              'data-tooltip': '已完成付款',
            },
            [
              h(
                NIcon,
                {},
                {
                  default: () =>
                    h('i', {
                      class: !row.actualPaymentTime ? 'fa fa-circle-o' : 'fa fa-check-circle',
                    }),
                }
              ),
            ]
          ),
        ])
      },
    },
  ])

  // 数据变更标记
  const dataChanged = ref(false)

  // 生成报销任务
  const goGenerateWaitReimApply = async (row: any) => {
    try {
      // 如果数据已变更，先刷新数据再操作
      if (dataChanged.value) {
        await queryPaymentDetails(props.contract.id)
        dataChanged.value = false

        // 重新查找当前行
        const updatedRow = waitPayReimData.value.find(item => item.id === row.id)
        if (!updatedRow) {
          window.$message.error('操作失败，未找到对应的付款记录')
          return
        }
        row = updatedRow
      }

      // 获取当前数据数组（根据当前模式选择不同的数组）
      const dataArray = showPayTable.value ? paymentData.value : waitPayReimData.value

      // 获取当前行的序号，用于确定之前的行
      const currentRowIndex = dataArray.findIndex(item => item.id === row.id)
      if (currentRowIndex > 0) {
        // 检查之前的付款计划是否都已处理（非默认状态）
        const previousRows = dataArray.slice(0, currentRowIndex)
        const hasDefaultStateRows = previousRows.some(prevRow => !isRowNotDefaultState(prevRow))

        if (hasDefaultStateRows) {
          // 使用确认框提示用户
          if (
            !(await window.$dialog.warning({
              title: '前置付款计划未处理',
              content: '发现前面的付款计划中存在未处理的记录，建议先处理前面的付款计划。是否仍要继续？',
              positiveText: '继续',
              negativeText: '取消',
            }))
          ) {
            return
          }
        }
      }

      const res = await goGenerateReim({ id: row.id })
      if (res.code === '200') {
        window.$message.success('生成报销任务成功')
        // 操作成功后刷新数据
        await queryPaymentDetails(props.contract.id)
      } else {
        window.$message.error(res.msg || '生成报销任务失败')
      }
    } catch (error) {
      console.error('生成报销任务失败:', error)
      window.$message.error('生成报销任务失败')
    }
  }

  // 跳转到报销页面
  const goEcsContractReim = (row: any) => {
    router.push({
      path: '/ecs/reimNew/contractReim',
      query: {
        ctCode: props.contract.ctCode,
      },
    })
  }

  // 添加新行
  const addNewRow = () => {
    const newIndex = paymentData.value.length
    const newRow = {
      stage: generateStageNameByPaymentType(editForm.paymentType, newIndex, editForm.paymentTerms),
      requirements: '',
      proportion: '',
      totalAmt: '',
      paymentTime: '',
      seq: newIndex + 1,
      reimStatusFlag: null,
    }
    paymentData.value.push(newRow)
    editForm.paymentTerms = paymentData.value.length

    // 如果从一次性付款变为多期付款，自动更新付款类型
    if (editForm.paymentType === '1' && paymentData.value.length > 1) {
      editForm.paymentType = '2' // 更新为分期付款
    }
  }

  // 删除行
  const deleteRow = (row: any) => {
    if (row.reimStatusFlag) {
      window.$message.error('该记录已有报销状态，不能删除')
      return
    }
    const index = paymentData.value.findIndex(item => item.seq === row.seq)
    if (index !== -1) {
      paymentData.value.splice(index, 1)
      // 更新序号
      paymentData.value.forEach((item, i) => {
        item.seq = i + 1
      })
      // 更新付款期数
      editForm.paymentTerms = paymentData.value.length

      // 如果删除后只剩一条记录，自动更新付款类型为一次性付款
      if (paymentData.value.length === 1 && editForm.paymentType === '2') {
        editForm.paymentType = '1' // 更新为一次性付款
      }
    }
  }

  // 计算已编制的比例总和
  const calculateTotalProportion = () => {
    return paymentData.value.reduce((sum, item) => sum + (Number(item.proportion) || 0), 0)
  }

  // 计算已编制的金额总和
  const calculateTotalAmount = () => {
    return paymentData.value.reduce((sum, item) => sum + (Number(item.totalAmt) || 0), 0)
  }

  // 计算报销计划的比例总和
  const calculateTotalReimProportion = () => {
    return waitPayReimData.value.reduce((sum, item) => sum + (Number(item.proportion) || 0), 0)
  }

  // 计算报销计划的金额总和
  const calculateTotalReimAmount = () => {
    return waitPayReimData.value.reduce((sum, item) => sum + (Number(item.totalAmt) || 0), 0)
  }

  // 获取timeline item类型
  const getTimelineItemType = (row: any) => {
    // 已付款
    if (row.actualPaymentTime) {
      return 'success'
    }
    // 已提交报销
    else if (row.reimId && row.reimburseTime) {
      return 'success'
    }
    // 已生成报销任务
    else if (row.pushTaskTime && !row.reimId) {
      return 'info'
    }
    // 待推送报销任务
    else if (!row.pushTaskTime) {
      return 'warning'
    }
    // 原报销审批中
    else {
      return 'warning'
    }
  }

  // 获取timeline icon class
  const getTimelineIconClass = (row: any) => {
    // 已付款
    if (row.actualPaymentTime) {
      return 'fa fa-check-circle'
    }
    // 已提交报销
    else if (row.reimId && row.reimburseTime) {
      return 'fa fa-check-circle'
    }
    // 已生成报销任务
    else if (row.pushTaskTime && !row.reimId) {
      return 'fa fa-info-circle'
    }
    // 待推送报销任务
    else if (!row.pushTaskTime) {
      return 'fa fa-exclamation-circle'
    }
    // 原报销审批中
    else {
      return 'fa fa-exclamation-circle'
    }
  }

  // 获取amount block颜色
  const getAmountBlockColor = (row: any) => {
    // 已付款
    if (row.actualPaymentTime) {
      return '#2b9f5e' // 深绿色
    }
    // 已提交报销
    else if (row.reimId && row.reimburseTime) {
      return '#5cb87a' // 绿色
    }
    // 已生成报销任务
    else if (row.pushTaskTime && !row.reimId) {
      return '#63b0ff' // 蓝色
    }
    // 待推送报销任务
    else if (!row.pushTaskTime) {
      return '#fcbd71' // 橙色
    }
    // 原报销审批中
    else {
      return '#f2cd36' // 黄色
    }
  }

  // 获取报销状态文本
  const getReimStatusText = (row: any) => {
    // 已付款
    if (row.actualPaymentTime) {
      return '已付款'
    }
    // 已提交报销
    else if (row.reimId && row.reimburseTime) {
      return '已提交报销，报销中'
    }
    // 已生成报销任务
    else if (row.pushTaskTime && !row.reimId) {
      return '待提交报销申请'
    }
    // 待推送报销任务
    else if (!row.pushTaskTime) {
      return '待推送报销任务'
    }
    // 原报销审批中
    else {
      return '原报销审批中'
    }
  }

  // 计算报销计划的报销进度百分比
  const calculateReimReimbursementPercentage = () => {
    const totalReimAmount = calculateTotalReimAmount()
    const reimbursedAmount = calculateReimReimbursedAmount()
    return totalReimAmount > 0 ? Math.round((reimbursedAmount / totalReimAmount) * 100) : 0
  }

  // 计算报销计划的已报销金额
  const calculateReimReimbursedAmount = () => {
    return waitPayReimData.value.reduce((sum, item) => {
      if (item.reimStatusFlag === '2') {
        return sum + Number(item.totalAmt)
      }
      return sum
    }, 0)
  }

  // 计算报销进度百分比
  const calculateReimbursementPercentage = () => {
    const totalReimAmount = calculateTotalAmount()
    const reimbursedAmount = calculateReimbursedAmount()
    return totalReimAmount > 0 ? Math.round((reimbursedAmount / totalReimAmount) * 100) : 0
  }

  // 计算已报销金额
  const calculateReimbursedAmount = () => {
    return paymentData.value.reduce((sum, item) => {
      if (item.reimStatusFlag === '2') {
        return sum + Number(item.totalAmt)
      }
      return sum
    }, 0)
  }

  // 获取节点背景颜色
  const getNodeBackgroundColor = (row: any) => {
    // 已付款
    if (row.actualPaymentTime) {
      return '#e8f5e9' // 深绿色背景
    }
    // 已提交报销
    else if (row.reimId && row.reimburseTime) {
      return '#e8f5e9' // 浅绿色背景
    }
    // 已生成报销任务
    else if (row.pushTaskTime && !row.reimId) {
      return '#e8f4ff' // 浅蓝色背景
    }
    // 待推送报销任务
    else if (!row.pushTaskTime) {
      return '#fff8e6' // 浅黄色背景
    }
    // 原报销审批中
    else {
      return '#fffff0' // 米黄色背景
    }
  }

  // 付款方式变更时更新付款阶段名称
  const onPaymentTypeChange = () => {
    // 如果付款方式变更为一次性付款，且当前有多条记录，则自动调整为分期付款（仅对一次性付款和分期付款进行处理）
    if (editForm.paymentType === '1' && paymentData.value.length > 1) {
      editForm.paymentType = '2' // 更新为分期付款
    }

    // 更新所有未报销记录的付款阶段名称
    paymentData.value.forEach((item, index) => {
      if (!item.reimStatusFlag) {
        item.stage = generateStageNameByPaymentType(editForm.paymentType, index, paymentData.value.length)
      }
    })
  }

  // 付款要求词条相关
  const showPaymentRequirementModal = ref(false)
  const selectedRowIndex = ref(0)
  const copyModeEnabled = ref(false)

  // 常用付款要求词条
  const paymentRequirementTerms = [
    { id: 1, content: '合同签订后支付' },
    { id: 2, content: '货物验收合格后支付' },
    { id: 3, content: '服务完成后支付' },
    { id: 4, content: '项目验收通过后支付' },
    { id: 5, content: '提交发票后30天内支付' },
    { id: 6, content: '工程进度达到50%后支付' },
    { id: 7, content: '安装调试完成后支付' },
    { id: 8, content: '试运行合格后支付' },
    { id: 9, content: '质保期满后支付' },
    { id: 10, content: '按月支付服务费' },
  ]

  // 显示付款要求词条弹窗
  const showPaymentRequirementTerms = () => {
    // 查找第一个可编辑的行
    const firstEditableIndex = paymentData.value.findIndex(item => !item.reimStatusFlag)
    if (firstEditableIndex >= 0) {
      selectedRowIndex.value = firstEditableIndex
    } else {
      window.$message.warning('没有可编辑的付款记录')
      return
    }
    showPaymentRequirementModal.value = true
  }

  // 选择行
  const selectRow = (index: number) => {
    // 检查行是否可以被选择（没有报销状态）
    if (paymentData.value[index] && !paymentData.value[index].reimStatusFlag) {
      selectedRowIndex.value = index
    } else {
      window.$message.warning('该行已有报销状态，无法修改')
    }
  }

  // 处理付款要求词条
  const handleTermClick = async (term: string) => {
    if (copyModeEnabled.value) {
      // 复制模式：将词条复制到剪贴板
      try {
        await navigator.clipboard.writeText(term)
        window.$message.success('词条已复制到剪贴板')
      } catch (err) {
        // 降级方案，兼容旧浏览器
        const tempInput = document.createElement('input')
        tempInput.value = term
        document.body.appendChild(tempInput)
        tempInput.select()
        document.execCommand('copy')
        document.body.removeChild(tempInput)
        window.$message.success('词条已复制到剪贴板')
      }
    } else {
      // 应用模式：将词条应用到选中的行
      if (
        paymentData.value.length > 0 &&
        selectedRowIndex.value >= 0 &&
        selectedRowIndex.value < paymentData.value.length
      ) {
        const row = paymentData.value[selectedRowIndex.value]
        // 严格检查：确保只有非报销状态的行才能被修改
        if (!row.reimStatusFlag) {
          row.requirements = term
          window.$message.success('词条已应用到选中行')
        } else {
          window.$message.error('该行已有报销状态，不能应用词条')
          // 重新选择第一个可编辑的行
          const firstEditableIndex = paymentData.value.findIndex(item => !item.reimStatusFlag)
          if (firstEditableIndex >= 0) {
            selectedRowIndex.value = firstEditableIndex
            window.$message.info('已自动选中第一个可编辑的行')
          }
        }
      } else {
        window.$message.error('请先选择有效的行')
      }
    }
  }

  // 快速设定比例相关
  const showProportionModal = ref(false)

  // 预设的比例方案
  const proportionPresets = [
    {
      id: 1,
      name: '均分方案',
      description: '将总额平均分配到各个付款阶段',
      apply: () => {
        if (paymentData.value.length === 0) return

        // 使用计算属性获取可编辑的行
        if (editableRows.value.length === 0) {
          window.$message.error('所有记录都已有报销状态，无法设置比例')
          return
        }

        const equalProportion = 100 / editableRows.value.length
        paymentData.value.forEach(item => {
          if (!item.reimStatusFlag) {
            item.proportion = equalProportion.toFixed(2)
            item.totalAmt = ((equalProportion / 100) * editForm.totalAmt).toFixed(2)
          }
        })
        showProportionModal.value = false
      },
    },
    {
      id: 2,
      name: '3-3-4方案',
      description: '30% + 30% + 40%的三期付款方案',
      apply: () => {
        // 使用计算属性获取可编辑的行
        if (editableRows.value.length === 0) {
          window.$message.error('所有记录都已有报销状态，无法设置比例')
          return
        }

        if (editableRows.value.length !== 3) {
          window.$message.warning('此方案仅适用于3个可编辑的付款阶段')
          return
        }

        const proportions = [30, 30, 40]
        let editableIndex = 0

        paymentData.value.forEach(item => {
          if (!item.reimStatusFlag) {
            if (editableIndex < 3) {
              item.proportion = proportions[editableIndex]
              item.totalAmt = ((proportions[editableIndex] / 100) * editForm.totalAmt).toFixed(2)
              editableIndex++
            }
          }
        })
        showProportionModal.value = false
      },
    },
    {
      id: 3,
      name: '2-3-5方案',
      description: '20% + 30% + 50%的三期付款方案',
      apply: () => {
        // 使用计算属性获取可编辑的行
        if (editableRows.value.length === 0) {
          window.$message.error('所有记录都已有报销状态，无法设置比例')
          return
        }

        if (editableRows.value.length !== 3) {
          window.$message.warning('此方案仅适用于3个可编辑的付款阶段')
          return
        }

        const proportions = [20, 30, 50]
        let editableIndex = 0

        paymentData.value.forEach(item => {
          if (!item.reimStatusFlag) {
            if (editableIndex < 3) {
              item.proportion = proportions[editableIndex]
              item.totalAmt = ((proportions[editableIndex] / 100) * editForm.totalAmt).toFixed(2)
              editableIndex++
            }
          }
        })
        showProportionModal.value = false
      },
    },
    {
      id: 4,
      name: '4-3-3方案',
      description: '40% + 30% + 30%的三期付款方案',
      apply: () => {
        // 使用计算属性获取可编辑的行
        if (editableRows.value.length === 0) {
          window.$message.error('所有记录都已有报销状态，无法设置比例')
          return
        }

        if (editableRows.value.length !== 3) {
          window.$message.warning('此方案仅适用于3个可编辑的付款阶段')
          return
        }

        const proportions = [40, 30, 30]
        let editableIndex = 0

        paymentData.value.forEach(item => {
          if (!item.reimStatusFlag) {
            if (editableIndex < 3) {
              item.proportion = proportions[editableIndex]
              item.totalAmt = ((proportions[editableIndex] / 100) * editForm.totalAmt).toFixed(2)
              editableIndex++
            }
          }
        })
        showProportionModal.value = false
      },
    },
    {
      id: 5,
      name: '递减方案',
      description: '按递减比例分配（适用于多期付款）',
      apply: () => {
        // 使用计算属性获取可编辑的行
        if (editableRows.value.length === 0) {
          window.$message.error('所有记录都已有报销状态，无法设置比例')
          return
        }

        if (editableRows.value.length <= 1) {
          window.$message.warning('此方案需要至少2个可编辑的付款阶段')
          return
        }

        const n = editableRows.value.length
        let sum = (n * (n + 1)) / 2 // 等差数列求和公式
        let editableIndex = 0

        paymentData.value.forEach(item => {
          if (!item.reimStatusFlag) {
            const proportion = ((n - editableIndex) / sum) * 100
            item.proportion = proportion.toFixed(2)
            item.totalAmt = ((proportion / 100) * editForm.totalAmt).toFixed(2)
            editableIndex++
          }
        })
        showProportionModal.value = false
      },
    },
    {
      id: 6,
      name: '递增方案',
      description: '按递增比例分配（适用于多期付款）',
      apply: () => {
        // 使用计算属性获取可编辑的行
        if (editableRows.value.length === 0) {
          window.$message.error('所有记录都已有报销状态，无法设置比例')
          return
        }

        if (editableRows.value.length <= 1) {
          window.$message.warning('此方案需要至少2个可编辑的付款阶段')
          return
        }

        const n = editableRows.value.length
        let sum = (n * (n + 1)) / 2 // 等差数列求和公式
        let editableIndex = 0

        paymentData.value.forEach(item => {
          if (!item.reimStatusFlag) {
            const proportion = ((editableIndex + 1) / sum) * 100
            item.proportion = proportion.toFixed(2)
            item.totalAmt = ((proportion / 100) * editForm.totalAmt).toFixed(2)
            editableIndex++
          }
        })
        showProportionModal.value = false
      },
    },
  ]

  // 显示快速设定比例弹窗
  const showQuickProportionSetting = () => {
    showProportionModal.value = true
  }

  // 自定义比例输入
  const customProportionInput = ref('')

  // 应用自定义比例
  const applyCustomProportion = () => {
    // 使用计算属性获取可编辑的行
    if (editableRows.value.length === 0) {
      window.$message.error('所有记录都已有报销状态，无法设置比例')
      return
    }

    // 解析输入的数字
    const inputValues = customProportionInput.value
      .trim()
      .split(/\s+/)
      .map(Number)
      .filter(n => !isNaN(n) && n >= 0)

    // 验证输入
    if (inputValues.length === 0) {
      window.$message.error('请输入有效的数字')
      return
    }

    if (inputValues.length !== editableRows.value.length) {
      window.$message.error(`请输入${editableRows.value.length}个数字，与可编辑行数一致`)
      return
    }

    // 计算总和
    const sum = inputValues.reduce((acc, val) => acc + val, 0)
    if (sum === 0) {
      window.$message.error('输入的数字总和不能为0')
      return
    }

    // 计算比例并应用
    let editableIndex = 0
    paymentData.value.forEach(item => {
      if (!item.reimStatusFlag) {
        // 计算百分比
        const proportion = (inputValues[editableIndex] / sum) * 100
        item.proportion = proportion.toFixed(2)
        // 计算金额
        item.totalAmt = ((proportion / 100) * editForm.totalAmt).toFixed(2)
        editableIndex++
      }
    })

    window.$message.success('比例设置成功')
    customProportionInput.value = '' // 清空输入
    showProportionModal.value = false
  }

  // 节点详情模态框
  const showDetailModal = ref(false)
  const selectedNode = ref<any>(null)

  // 计算属性：是否有推送任务或报销信息
  const hasPushTaskOrReimburse = computed(() => {
    if (!selectedNode.value) return false
    return !!(
      selectedNode.value.pushTaskTime ||
      selectedNode.value.reimburseTime ||
      selectedNode.value.reimId ||
      selectedNode.value.reimburseNo ||
      selectedNode.value.reimbursePerson ||
      selectedNode.value.reimburseDept ||
      selectedNode.value.actualPaymentTime
    )
  })

  // 显示节点详情
  const showNodeDetail = (node: any) => {
    selectedNode.value = node
    showDetailModal.value = true
  }

  // 定义报销流程的步骤
  const reimbursementSteps = [
    {
      value: 'waiting',
      title: '待推送报销任务',
      description: '合同付款计划已编制，等待推送为报销任务',
    },
    {
      value: 'pushed',
      title: '已推送报销任务',
      description: '已推送为报销任务，等待提交报销申请',
    },
    {
      value: 'submitted',
      title: '已提交报销申请',
      description: '报销申请已提交，等待审批',
    },
    {
      value: 'approving',
      title: '报销审批中',
      description: '报销申请正在审批流程中',
    },
    {
      value: 'paid',
      title: '已完成付款',
      description: '报销已完成，款项已支付',
    },
  ]

  // 获取步骤状态
  const getStepStatus = (node: any, stepValue: string) => {
    if (!node) return 'pending'

    // 已付款
    if (node.actualPaymentTime) {
      return stepValue === 'paid'
        ? 'current'
        : ['waiting', 'pushed', 'submitted', 'approving'].includes(stepValue)
        ? 'completed'
        : 'pending'
    }
    // 已提交报销，报销中
    else if (node.reimId && node.reimburseTime) {
      return stepValue === 'approving'
        ? 'current'
        : ['waiting', 'pushed', 'submitted'].includes(stepValue)
        ? 'completed'
        : 'pending'
    }
    // 已提交报销申请
    else if (node.reimId) {
      return stepValue === 'submitted' ? 'current' : ['waiting', 'pushed'].includes(stepValue) ? 'completed' : 'pending'
    }
    // 已推送报销任务
    else if (node.pushTaskTime) {
      return stepValue === 'pushed' ? 'current' : stepValue === 'waiting' ? 'completed' : 'pending'
    }
    // 待推送报销任务
    else {
      return stepValue === 'waiting' ? 'current' : 'pending'
    }
  }

  // 计算进度百分比
  const calculateProgressPercentage = (item: any) => {
    const totalSteps = 5
    const completedSteps = [
      hasReachedStep(item, 1),
      hasReachedStep(item, 2),
      hasReachedStep(item, 3),
      hasReachedStep(item, 4),
      hasReachedStep(item, 5),
    ].filter(Boolean).length
    return Math.round((completedSteps / totalSteps) * 100)
  }

  // 检查是否达到某个步骤
  const hasReachedStep = (item: any, step: number) => {
    switch (step) {
      case 1:
        return !!item.paymentTime
      case 2:
        return !!item.pushTaskTime
      case 3:
        return !!item.reimburseTime
      case 4:
        return !!item.reimId
      case 5:
        return !!item.actualPaymentTime
      default:
        return false
    }
  }

  // 获取当前步骤
  const getCurrentStep = (item: any) => {
    if (!item.paymentTime) return 1
    if (!item.pushTaskTime) return 2
    if (!item.reimburseTime) return 3
    if (!item.reimId) return 4
    return 5
  }

  // 计算属性：是否有非默认状态的记录
  const hasNonDefaultStatusRows = computed(() => {
    return paymentData.value.some(item => !!item.reimStatusFlag)
  })

  // 判断是否完成某个步骤（编辑模式）
  const hasCompletedStep = (stepIndex: number) => {
    // 检查是否有至少一个付款记录已经完成这个步骤
    return paymentData.value.some(item => {
      // 最后一个步骤：已完成付款
      if (stepIndex === 4) {
        return !!item.actualPaymentTime
      }
      // 第四个步骤：报销审批中
      else if (stepIndex === 3) {
        return !!item.reimId && !!item.reimburseTime && !item.actualPaymentTime
      }
      // 第三个步骤：已提交报销申请
      else if (stepIndex === 2) {
        return !!item.reimId
      }
      // 第二个步骤：已推送报销任务
      else if (stepIndex === 1) {
        return !!item.pushTaskTime
      }
      // 第一个步骤：默认完成
      else {
        return true
      }
    })
  }

  // 判断当前是哪个步骤（编辑模式）
  const isCurrentStep = (stepIndex: number) => {
    // 检查是否有付款记录当前处于这个步骤
    let hasCurrentStep = false
    let hasNextStep = false

    paymentData.value.forEach(item => {
      // 第一个步骤：待推送报销任务
      if (stepIndex === 0 && !item.pushTaskTime) {
        hasCurrentStep = true
      }
      // 第二个步骤：已推送报销任务
      else if (stepIndex === 1 && item.pushTaskTime && !item.reimId) {
        hasCurrentStep = true
      }
      // 第三个步骤：已提交报销申请
      else if (stepIndex === 2 && item.reimId && !item.reimburseTime) {
        hasCurrentStep = true
      }
      // 第四个步骤：报销审批中
      else if (stepIndex === 3 && item.reimId && item.reimburseTime && !item.actualPaymentTime) {
        hasCurrentStep = true
      }
      // 第五个步骤：已完成付款
      else if (stepIndex === 4 && item.actualPaymentTime) {
        hasCurrentStep = true
      }

      // 检查是否有下一个步骤
      if (stepIndex < 4) {
        const nextStepIndex = stepIndex + 1
        if (nextStepIndex === 1 && item.pushTaskTime) {
          hasNextStep = true
        } else if (nextStepIndex === 2 && item.reimId) {
          hasNextStep = true
        } else if (nextStepIndex === 3 && item.reimId && item.reimburseTime) {
          hasNextStep = true
        } else if (nextStepIndex === 4 && item.actualPaymentTime) {
          hasNextStep = true
        }
      }
    })

    // 如果当前步骤有记录处于该状态，且没有记录处于下一个步骤，则认为是当前步骤
    return hasCurrentStep && !hasNextStep
  }

  // 判断是否是待处理步骤（编辑模式）
  const isPendingStep = (stepIndex: number) => {
    return !hasCompletedStep(stepIndex) && !isCurrentStep(stepIndex)
  }

  // 判断是否完成某个步骤（查看模式）
  const hasCompletedReimStep = (stepIndex: number) => {
    // 检查是否有至少一个付款记录已经完成这个步骤
    return waitPayReimData.value.some(item => {
      // 最后一个步骤：已完成付款
      if (stepIndex === 4) {
        return !!item.actualPaymentTime
      }
      // 第四个步骤：报销审批中
      else if (stepIndex === 3) {
        return !!item.reimId && !!item.reimburseTime && !item.actualPaymentTime
      }
      // 第三个步骤：已提交报销申请
      else if (stepIndex === 2) {
        return !!item.reimId
      }
      // 第二个步骤：已推送报销任务
      else if (stepIndex === 1) {
        return !!item.pushTaskTime
      }
      // 第一个步骤：默认完成
      else {
        return true
      }
    })
  }

  // 判断当前是哪个步骤（查看模式）
  const isCurrentReimStep = (stepIndex: number) => {
    // 检查是否有付款记录当前处于这个步骤
    let hasCurrentStep = false
    let hasNextStep = false

    waitPayReimData.value.forEach(item => {
      // 第一个步骤：待推送报销任务
      if (stepIndex === 0 && !item.pushTaskTime) {
        hasCurrentStep = true
      }
      // 第二个步骤：已推送报销任务
      else if (stepIndex === 1 && item.pushTaskTime && !item.reimId) {
        hasCurrentStep = true
      }
      // 第三个步骤：已提交报销申请
      else if (stepIndex === 2 && item.reimId && !item.reimburseTime) {
        hasCurrentStep = true
      }
      // 第四个步骤：报销审批中
      else if (stepIndex === 3 && item.reimId && item.reimburseTime && !item.actualPaymentTime) {
        hasCurrentStep = true
      }
      // 第五个步骤：已完成付款
      else if (stepIndex === 4 && item.actualPaymentTime) {
        hasCurrentStep = true
      }

      // 检查是否有下一个步骤
      if (stepIndex < 4) {
        const nextStepIndex = stepIndex + 1
        if (nextStepIndex === 1 && item.pushTaskTime) {
          hasNextStep = true
        } else if (nextStepIndex === 2 && item.reimId) {
          hasNextStep = true
        } else if (nextStepIndex === 3 && item.reimId && item.reimburseTime) {
          hasNextStep = true
        } else if (nextStepIndex === 4 && item.actualPaymentTime) {
          hasNextStep = true
        }
      }
    })

    // 如果当前步骤有记录处于该状态，且没有记录处于下一个步骤，则认为是当前步骤
    return hasCurrentStep && !hasNextStep
  }

  // 判断是否是待处理步骤（查看模式）
  const isPendingReimStep = (stepIndex: number) => {
    return !hasCompletedReimStep(stepIndex) && !isCurrentReimStep(stepIndex)
  }

  // 🆕 新增：获取组织机构显示信息
  const getUseOrgDisplay = () => {
    return useOrgInfo.value.name
  }

  const getUseOrgPersonDisplay = () => {
    return useOrgPersonInfo.value.name
  }

  const getManageOrgDisplay = () => {
    return manageOrgInfo.value.name
  }

  const getResponsiblePersonDisplay = () => {
    return responsiblePersonInfo.value.name
  }
</script>

<style lang="scss" scoped>
  .contract-payment-reim {
    width: 100%;
    height: 100%;
  }

  /* 非默认状态行的标记样式 */
  :deep(.n-data-table .n-base-selection-disabled) {
    background-color: #f5f7fa;
  }

  /* 禁用输入框样式 */
  :deep(.n-input.n-input--disabled) {
    background-color: #f0f2f5;
    border-color: #ddd;
    cursor: not-allowed;
  }

  /* 禁用数字输入框样式 */
  :deep(.n-input-number.n-input-number--disabled) {
    background-color: #f0f2f5;
    border-color: #ddd;
    cursor: not-allowed;
  }

  /* 禁用日期选择器样式 */
  :deep(.n-date-picker.n-date-picker--disabled) {
    background-color: #f0f2f5;
    border-color: #ddd;
    cursor: not-allowed;
  }

  .payment-progress-timeline {
    margin-top: 24px;

    .timeline-title {
      margin-bottom: 16px;
      font-weight: bold;
    }
  }

  .payment-amount-distribution {
    margin-top: 24px;

    .timeline-title {
      margin-bottom: 16px;
      font-weight: bold;
    }

    /* 新的进度条样式 */
    .progress-bar-wrapper {
      position: relative;
      margin-bottom: 48px;
    }

    .progress-bar-container {
      display: flex;
      height: 24px;
      background-color: #f5f7fa;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .progress-bar-segment {
      height: 100%;
      transition: all 0.3s ease;
      position: relative;
      cursor: pointer;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 1px;
        background-color: rgba(255, 255, 255, 0.7);
      }

      &:hover {
        transform: scaleY(1.2);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        z-index: 2;

        .progress-segment-tooltip {
          display: block;
        }
      }

      .progress-segment-tooltip {
        display: none;
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.75);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        &::after {
          content: '';
          position: absolute;
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          border-width: 5px;
          border-style: solid;
          border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;
        }
      }
    }

    .progress-bar-labels {
      position: relative;
      height: 40px;
      margin-top: 8px;
    }

    .progress-label {
      position: absolute;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .progress-label-marker {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-bottom: 4px;
    }

    .progress-label-text {
      font-size: 12px;
      color: #666;
      white-space: nowrap;
    }

    .amount-distribution-legend {
      margin-top: 12px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .legend-item {
        display: flex;
        align-items: center;
        margin-right: 24px;

        .legend-color {
          width: 16px;
          height: 16px;
          border-radius: 4px;
          margin-right: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        span {
          font-size: 12px;
        }
      }
    }
  }

  .reimbursement-progress-animation {
    margin-top: 24px;
    background-color: #f9fcff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .timeline-title {
      margin-bottom: 16px;
      font-weight: bold;
      font-size: 16px;
      color: #333;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background-color: #18a058;
        margin-right: 8px;
        border-radius: 2px;
      }
    }

    // 纵向多分支、横向状态流程图样式
    .flow-diagram-vertical {
      width: 100%;
      padding: 16px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

      .flow-source-container {
        display: flex;
        justify-content: center;
        margin-bottom: 24px;
      }

      .flow-branches-container {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 40px;
      }

      .flow-branch-vertical {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 140px;
      }

      .flow-connector-vertical {
        position: relative;
        width: 2px;
        height: 40px;
        margin-bottom: 8px;

        .flow-line-vertical {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 2px;
          height: 100%;
          background-color: #d9d9d9;
        }

        .flow-arrow-vertical {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%) rotate(45deg);
          width: 8px;
          height: 8px;
          border-right: 2px solid #d9d9d9;
          border-bottom: 2px solid #d9d9d9;
        }
      }

      .flow-target-container {
        width: 100%;
      }

      .flow-node {
        padding: 12px;
        border-radius: 8px;
        text-align: center;
        width: 100%;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .flow-node-title {
          font-weight: bold;
          font-size: 14px;
          margin-bottom: 4px;
        }

        .flow-node-count {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 4px;
        }

        .flow-node-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 12px;
          display: inline-block;
          background-color: rgba(0, 0, 0, 0.05);
        }
      }

      .flow-source-node {
        background-color: #e8f5e9;
        color: #2e7d32;
      }

      .flow-target-node {
        background-color: #fff;
        color: #555;
        border: 1px solid #eee;

        &.flow-node-active {
          background-color: #e3f2fd;
          color: #1976d2;
          border-color: #bbdefb;
        }
      }
    }

    // 报销进度指示器
    .reimbursement-progress-indicator {
      .progress-text {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 报销状态步骤样式
  .reimbursement-status-steps {
    margin-bottom: 24px;

    .status-steps-title {
      font-weight: bold;
      margin-bottom: 16px;
    }

    .status-steps-container {
      display: flex;
      justify-content: space-between;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 24px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #e0e0e0;
        z-index: 0;
      }

      .status-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 1;
        flex: 1;

        .step-number {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: #f5f5f5;
          border: 2px solid #e0e0e0;
          color: #999;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          margin-bottom: 8px;
        }

        .step-content {
          text-align: center;
          padding: 0 8px;

          .step-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 4px;
            color: #999;
          }

          .step-description {
            font-size: 12px;
            color: #999;
            max-width: 120px;
          }
        }

        .step-connector {
          position: absolute;
          top: 24px;
          right: -50%;
          width: 100%;
          height: 2px;
          z-index: -1;
        }

        &.status-step-completed {
          .step-number {
            background-color: #e8f5e9;
            border-color: #4caf50;
            color: #4caf50;
          }

          .step-title {
            color: #4caf50;
          }

          .step-description {
            color: #666;
          }

          .step-connector {
            background-color: #4caf50;
          }
        }

        &.status-step-current {
          .step-number {
            background-color: #4caf50;
            border-color: #4caf50;
            color: white;
          }

          .step-title {
            color: #4caf50;
            font-weight: bold;
          }

          .step-description {
            color: #666;
          }
        }
      }
    }
  }

  // 节点颜色
  .node-color-default {
    color: #666;
    background-color: #f5f5f5;
  }

  .node-color-waiting {
    color: #ff9800;
    background-color: #fff8e1;
  }

  .node-color-pushed {
    color: #2196f3;
    background-color: #e3f2fd;
  }

  .node-color-submitted {
    color: #9c27b0;
    background-color: #f3e5f5;
  }

  .node-color-approved {
    color: #4caf50;
    background-color: #e8f5e9;
  }

  .node-color-paid {
    color: #2e7d32;
    background-color: #e8f5e9;
  }

  /* 水平步骤进度条样式 */
  .steps-progress-container {
    position: relative;
    padding: 40px 0;
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .steps-line {
    position: absolute;
    top: 40px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #e0e0e0;
    z-index: 1;
  }

  .steps-items {
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    padding: 0 50px;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120px;

    .step-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
      background-color: #f5f5f5;
      border: 2px solid #e0e0e0;
      color: #999;
      font-weight: bold;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .step-label {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 6px;
      color: #999;
      text-align: center;
      transition: color 0.3s ease;
    }

    .step-description {
      font-size: 12px;
      color: #999;
      text-align: center;
      line-height: 1.4;
    }

    &.step-completed {
      .step-circle {
        background-color: #4caf50;
        border-color: #4caf50;
        color: white;
      }

      .step-label {
        color: #4caf50;
      }

      &::before {
        background-color: #4caf50;
      }
    }

    &.step-current {
      .step-circle {
        background-color: #4caf50;
        border-color: #4caf50;
        color: white;
        box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.2);
        transform: scale(1.1);
      }

      .step-label {
        color: #4caf50;
        font-weight: bold;
      }

      .step-description {
        color: #666;
      }
    }

    &.step-pending {
      .step-circle {
        background-color: #f5f5f5;
        border-color: #e0e0e0;
        color: #999;
      }

      .step-label {
        color: #999;
      }
    }
  }

  /* 相邻步骤之间连接线的样式 */
  .steps-items::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50px;
    right: 50px;
    height: 2px;
    background: linear-gradient(to right, #4caf50 var(--progress), #e0e0e0 var(--progress));
    --progress: 0%;
    z-index: -1;
  }

  /* 计算并设置进度条的宽度 */
  .steps-items:has(.step-completed:nth-child(2))::before {
    --progress: 25%;
  }

  .steps-items:has(.step-completed:nth-child(3))::before {
    --progress: 50%;
  }

  .steps-items:has(.step-completed:nth-child(4))::before {
    --progress: 75%;
  }

  .steps-items:has(.step-completed:nth-child(5))::before {
    --progress: 100%;
  }
</style>
