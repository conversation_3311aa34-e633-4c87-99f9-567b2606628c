# 🔧 后端接口字段缺失修复指南

## 📋 问题确认

根据接口返回数据分析，确认缺少以下字段：
- ❌ `useOrgPerson` - 使用科室负责人代码
- ❌ `useOrgPersonName` - 使用科室负责人姓名  
- ❌ `useOrgName` - 使用科室名称
- ❌ `manageOrgName` - 管理科室名称

## 🎯 修复目标

让接口返回完整的科室和负责人信息：
```json
{
  "useOrg": "521001",
  "useOrgName": "心内科",           // 🆕 新增
  "useOrgPerson": "0123",          // 🆕 新增
  "useOrgPersonName": "张三",       // 🆕 新增
  "manageOrg": "531001", 
  "manageOrgName": "设备科",        // 🆕 新增
  "responsiblePerson": "0155",
  "responsiblePersonName": "彭心梅"
}
```

## 🛠️ 具体修复步骤

### 步骤1: 修改 CmsContractVo.java

**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/vo/CmsContractVo.java`

在第47行 `private String useOrg;` 之后添加：

```java
/** 使用科室 */
private String useOrg;

/** 🆕 使用科室名称 */
private String useOrgName;

/** 🆕 使用科室负责人 */
private String useOrgPerson;

/** 🆕 使用科室负责人姓名 */
private String useOrgPersonName;

/** 管理科室 */
private String manageOrg;

/** 🆕 管理科室名称 */
private String manageOrgName;
```

### 步骤2: 修改 CmsContractReadMapper.xml

**文件路径**: `med-cms/src/main/resources/mapper/contractManage/read/CmsContractReadMapper.xml`

#### 2.1 修改 SELECT 语句（约第40-44行）

**查找这段代码**:
```xml
SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, cco.opposite_name AS
oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, co.ct_type_code AS
ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

**替换为**:
```xml
SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
-- 🆕 新增科室和负责人信息
use_org_info.org_name AS useOrgName, co.use_org_person AS useOrgPerson,
use_person_info.emp_name AS useOrgPersonName, co.manage_org AS manageOrg,
manage_org_info.org_name AS manageOrgName,
cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

#### 2.2 修改 FROM 和 JOIN 语句（约第62-66行）

**查找这段代码**:
```xml
FROM cms_contract co LEFT
JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code LEFT JOIN
hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN cms_contract_opposite cco ON
co.opposite_id = cco.id LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id =
coc.id LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

**替换为**:
```xml
FROM cms_contract co 
LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
-- 🆕 新增科室和员工信息关联
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

### 步骤3: 验证数据库关联

在修改代码之前，先执行这个SQL验证关联关系：

```sql
-- 验证关联查询（使用您的合同ID 336）
SELECT 
    co.id,
    co.ct_name,
    co.use_org,
    use_org_info.org_name as use_org_name,
    co.use_org_person,
    use_person_info.emp_name as use_org_person_name,
    co.manage_org,
    manage_org_info.org_name as manage_org_name,
    co.responsible_person,
    responsible_person_info.emp_name as responsible_person_name
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info responsible_person_info ON co.responsible_person = responsible_person_info.emp_code AND responsible_person_info.is_deleted = 0
WHERE co.id = 336;
```

### 步骤4: 可能的关联字段调整

如果步骤3的查询没有返回预期结果，可能需要调整关联条件：

#### 4.1 检查科室字段关联方式
```sql
-- 检查use_org字段是否关联org_code而不是org_id
SELECT 
    co.use_org,
    ho1.org_name as by_org_id,
    ho2.org_name as by_org_code
FROM cms_contract co
LEFT JOIN hrm_org ho1 ON co.use_org = ho1.org_id AND ho1.active_flag = '1'
LEFT JOIN hrm_org ho2 ON co.use_org = ho2.org_code AND ho2.active_flag = '1'
WHERE co.id = 336;
```

#### 4.2 如果需要使用org_code关联，修改JOIN语句：
```xml
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_code AND use_org_info.active_flag = '1'
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_code AND manage_org_info.active_flag = '1'
```

### 步骤5: 检查use_org_person字段是否有数据

```sql
-- 检查合同336的use_org_person字段
SELECT id, ct_name, use_org_person 
FROM cms_contract 
WHERE id = 336;

-- 检查所有合同的use_org_person字段使用情况
SELECT 
    COUNT(*) as total_contracts,
    COUNT(use_org_person) as has_use_org_person,
    COUNT(CASE WHEN use_org_person IS NOT NULL AND use_org_person != '' THEN 1 END) as has_non_empty_use_org_person
FROM cms_contract 
WHERE hospital_id = 'zjxrmyy' AND is_deleted = 0;
```

## 🧪 测试验证

修改完成后：

1. **重启后端服务**
2. **调用合同查询接口**
3. **检查返回数据是否包含新字段**：
   ```json
   {
     "useOrgName": "具体科室名称",
     "useOrgPerson": "员工代码",
     "useOrgPersonName": "员工姓名",
     "manageOrgName": "管理科室名称"
   }
   ```

## 🚨 注意事项

1. **如果use_org_person字段为空**：
   - 可能该合同确实没有设置使用科室负责人
   - 需要在合同录入时补充该信息

2. **关联字段可能的问题**：
   - `use_org` 可能存储的是 `org_code` 而不是 `org_id`
   - 需要根据实际数据调整JOIN条件

3. **性能考虑**：
   - 新增的JOIN可能影响查询性能
   - 建议添加相应的数据库索引

## 📋 预期结果

修复后，接口应该返回：
```json
{
  "useOrg": "521001",
  "useOrgName": "心内科",
  "useOrgPerson": "0123", 
  "useOrgPersonName": "张三",
  "manageOrg": "531001",
  "manageOrgName": "设备科",
  "responsiblePerson": "0155",
  "responsiblePersonName": "彭心梅"
}
```

前端就能正确显示科室名称和负责人姓名了！
