# 🔧 合同科室字段修复方案

## 📋 问题分析

根据代码分析，发现以下问题：

1. **CmsContractVo.java 缺少字段**: 缺少 `useOrgPerson`, `useOrgPersonName`, `useOrgName`, `manageOrgName` 等关键字段
2. **SQL查询不完整**: 当前SQL没有查询 `use_org_person` 字段，也没有关联 `hrm_org` 和 `hrm_employee_info` 表获取名称
3. **前端显示逻辑**: 前端已经优化，但后端数据不完整

## 🛠️ 修复步骤

### 步骤1: 修改 CmsContractVo.java

**文件路径**: `med-cms/src/main/java/com/jp/med/cms/modules/contractManage/vo/CmsContractVo.java`

在第47行 `private String useOrg;` 之后添加以下字段：

```java
/** 使用科室 */
private String useOrg;

/** 🆕 使用科室名称 */
private String useOrgName;

/** 🆕 使用科室负责人 */
private String useOrgPerson;

/** 🆕 使用科室负责人姓名 */
private String useOrgPersonName;

/** 管理科室 */
private String manageOrg;

/** 🆕 管理科室名称 */
private String manageOrgName;
```

### 步骤2: 修改 CmsContractReadMapper.xml

**文件路径**: `med-cms/src/main/resources/mapper/contractManage/read/CmsContractReadMapper.xml`

#### 2.1 修改 SELECT 子句 (第40-44行)

将现有的：
```xml
SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, cco.opposite_name AS
oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, co.ct_type_code AS
ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

修改为：
```xml
SELECT co.ID AS ID, co.ct_code AS ctCode, co.ct_unified_code AS ctUnifiedCode, co.ct_name AS
ctName, co.tender_num AS tenderNum, co.tender_name AS tenderName, co.bid_winning_time AS
bidWinningTime, co.sign_time AS signTime, co.use_org AS useOrg, 
-- 🆕 新增科室和负责人信息
use_org_info.org_name AS useOrgName, co.use_org_person AS useOrgPerson,
use_person_info.emp_name AS useOrgPersonName, co.manage_org AS manageOrg,
manage_org_info.org_name AS manageOrgName,
cco.opposite_name AS oppositeName, co.opposite_id AS oppositeId, co.main_id AS mainId, 
co.ct_type_code AS ctTypeCode, co.ct_status AS ctStatus, co.responsible_person AS responsiblePerson,
```

#### 2.2 修改 FROM 和 JOIN 子句 (第62-66行)

将现有的：
```xml
FROM cms_contract co LEFT
JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code LEFT JOIN
hrm_employee_info hei1 ON co.appyer = hei1.emp_code LEFT JOIN cms_contract_opposite cco ON
co.opposite_id = cco.id LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id =
coc.id LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

修改为：
```xml
FROM cms_contract co 
LEFT JOIN hrm_employee_info hei ON co.responsible_person = hei.emp_code 
LEFT JOIN hrm_employee_info hei1 ON co.appyer = hei1.emp_code 
-- 🆕 新增科室和员工信息关联
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
LEFT JOIN cms_contract_opposite cco ON co.opposite_id = cco.id 
LEFT JOIN cms_opposite_contacts coc ON co.opposite_person_id = coc.id 
LEFT JOIN cms_opposite_finance cof ON co.opposite_bank_id = cof.id
```

### 步骤3: 验证数据库表结构

在修改代码之前，请先执行以下SQL验证数据库表结构：

```sql
-- 1. 验证cms_contract表是否包含use_org_person字段
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'cms_contract' 
  AND column_name IN ('use_org', 'use_org_person', 'manage_org', 'responsible_person');

-- 2. 验证hrm_org表结构
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'hrm_org' 
  AND column_name IN ('org_id', 'org_name', 'active_flag');

-- 3. 验证hrm_employee_info表结构
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'hrm_employee_info' 
  AND column_name IN ('emp_code', 'emp_name', 'is_deleted');

-- 4. 测试关联查询是否正常
SELECT 
    co.id,
    co.ct_name,
    co.use_org,
    use_org_info.org_name as use_org_name,
    co.use_org_person,
    use_person_info.emp_name as use_org_person_name,
    co.manage_org,
    manage_org_info.org_name as manage_org_name
FROM cms_contract co
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_id AND use_org_info.active_flag = '1'
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_code AND use_person_info.is_deleted = 0
LEFT JOIN hrm_org manage_org_info ON co.manage_org = manage_org_info.org_id AND manage_org_info.active_flag = '1'
WHERE co.hospital_id = 'zjxrmyy' 
  AND co.is_deleted = 0
  AND (co.use_org IS NOT NULL OR co.use_org_person IS NOT NULL)
LIMIT 5;
```

### 步骤4: 可能的数据库字段问题

如果 `cms_contract` 表中没有 `use_org_person` 字段，需要先添加：

```sql
-- 检查字段是否存在
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'cms_contract' 
  AND column_name = 'use_org_person';

-- 如果字段不存在，添加字段
ALTER TABLE cms_contract 
ADD COLUMN use_org_person VARCHAR(64) COMMENT '使用科室负责人';
```

### 步骤5: 关联字段可能的问题

根据实际数据库情况，可能需要调整关联条件：

1. **如果 use_org 存储的是 org_code 而不是 org_id**:
```xml
LEFT JOIN hrm_org use_org_info ON co.use_org = use_org_info.org_code AND use_org_info.active_flag = '1'
```

2. **如果 use_org_person 存储的是员工姓名而不是 emp_code**:
```xml
LEFT JOIN hrm_employee_info use_person_info ON co.use_org_person = use_person_info.emp_name AND use_person_info.is_deleted = 0
```

### 步骤6: 性能优化

添加以下索引以提高查询性能：

```sql
-- 为hrm_org表添加复合索引
CREATE INDEX IF NOT EXISTS idx_hrm_org_id_active ON hrm_org(org_id, active_flag);
CREATE INDEX IF NOT EXISTS idx_hrm_org_code_active ON hrm_org(org_code, active_flag);

-- 为hrm_employee_info表添加复合索引
CREATE INDEX IF NOT EXISTS idx_hrm_employee_code_deleted ON hrm_employee_info(emp_code, is_deleted);

-- 为cms_contract表添加索引
CREATE INDEX IF NOT EXISTS idx_cms_contract_org_fields ON cms_contract(use_org, use_org_person, manage_org, responsible_person);
```

## 🧪 测试验证

修改完成后，请进行以下测试：

1. **重启后端服务**
2. **访问合同管理页面**
3. **检查合同基本信息是否正确显示**:
   - 使用科室应显示科室名称
   - 使用科室负责人应显示员工姓名
   - 管理科室应显示科室名称
   - 管理科室负责人应显示员工姓名

## 🚨 故障排查

如果修改后仍有问题：

1. **检查后端日志**是否有SQL错误
2. **检查数据库连接**是否正常
3. **验证字段映射**是否正确
4. **确认数据完整性**，特别是 hrm_org 和 hrm_employee_info 表的数据

## 📝 注意事项

1. **备份数据库**：修改前请备份相关表
2. **测试环境先行**：建议先在测试环境验证
3. **缓存清理**：如果使用了缓存，需要清理相关缓存
4. **前端缓存**：清理浏览器缓存确保获取最新数据

## 🎯 预期结果

修改完成后，合同基本信息将正确显示：
- ✅ 使用科室: `心内科` (而非代码)
- ✅ 使用科室负责人: `张三` (而非编码)
- ✅ 管理科室: `设备科` (而非代码)
- ✅ 管理科室负责人: `李四` (而非编码)
